module.exports = {
  apps: [
    {
      name: 'apigateway',
      script: './apigateway/app.js',
      cwd: '',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'development',
        PORT: 8000
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 8000
      },
      // Logging
      log_file: './logs/api-gateway/combined.log',
      out_file: './logs/api-gateway/out.log',
      error_file: './logs/api-gateway/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // Process management
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      restart_delay: 4000,
      
      // Health monitoring
      min_uptime: '10s',
      max_restarts: 10,
      
      // Advanced options
      kill_timeout: 5000,
      listen_timeout: 3000,
      
      // Environment file
      env_file: './apigateway/.env'
    },
    {
      name: 'user',
      script: './services/user/app.js',
      cwd: '',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'development',
        PORT: 5001
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 5001
      },
      // Logging
      log_file: './logs/user-service/combined.log',
      out_file: './logs/user-service/out.log',
      error_file: './logs/user-service/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // Process management
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      restart_delay: 4000,
      
      // Health monitoring
      min_uptime: '10s',
      max_restarts: 10,
      
      // Advanced options
      kill_timeout: 5000,
      listen_timeout: 3000,
      
      // Environment file
      env_file: './services/user/.env'
    },
     {
      name: 'catalog',
      script: './services/catalog/app.js',
      cwd: '',
      instances: 1,
      exec_mode: 'fork',
      interpreter: '/home/<USER>/.nvm/versions/node/v18.20.4/bin/node',
      env: {
        NODE_ENV: 'development',
        PORT: 5008
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 5008
      },
      // Logging
      log_file: './logs/catalog-service/combined.log',
      out_file: './logs/catalog-service/out.log',
      error_file: './logs/catalog-service/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',

      // Process management
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      restart_delay: 4000,

      // Health monitoring
      min_uptime: '10s',
      max_restarts: 10,

      // Advanced options
      kill_timeout: 5000,
      listen_timeout: 3000,

      // Environment file
      env_file: './services/catalog/.env'
    }
  ],

  // Deployment configuration (optional)
  deploy: {
    production: {
      user: 'node',
      host: 'your-server.com',
      ref: 'origin/main',
      repo: '**************:your-username/your-repo.git',
      path: '/var/www/production',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
};
