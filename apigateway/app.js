const express = require("express");
const cors = require("cors");
const proxy = require("express-http-proxy");
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '.env') });

// Import middleware
const authMiddleware = require('./api/middleware/authentication');
const requestLogger = require('./api/middleware/requestLogger');
const errorHandler = require('./api/middleware/errorHandler');

const app = express();
const PORT = process.env.PORT || 8000;
const NODE_ENV = process.env.NODE_ENV || 'development';

// Trust proxy for accurate IP addresses
app.set('trust proxy', 1);

// Basic middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request logging and ID generation
app.use(...requestLogger.getMiddleware(NODE_ENV));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'api-gateway',
    version: '1.0.0',
    environment: NODE_ENV
  });
});

// Proxy configuration with enhanced error handling
const createProxyOptions = (serviceName, serviceUrl) => {
  return {
    target: serviceUrl,
    changeOrigin: true,
    timeout: 30000, // 30 seconds timeout
    proxyReqOptDecorator: (proxyReqOpts, srcReq) => {
      // Add service name for logging
      srcReq.serviceName = serviceName;

      // Forward complete user context as a single header
      if (srcReq.user) {
        // Forward the complete user object as a JSON string
        proxyReqOpts.headers['x-user-context'] = JSON.stringify(srcReq.user);

        console.log(`[${srcReq.requestId}] Forwarding user context:`, JSON.stringify(srcReq.user, null, 2));
      }

      // Forward request ID
      proxyReqOpts.headers['x-request-id'] = srcReq.requestId;

      return proxyReqOpts;
    },
    userResDecorator: (proxyRes, proxyResData, userReq, userRes) => {
      // Log successful proxy
      console.log(`[${userReq.requestId}] Proxied to ${serviceName}: ${userReq.method} ${userReq.path} -> ${proxyRes.statusCode}`);
      return proxyResData;
    },
    proxyErrorHandler: errorHandler.proxyErrorHandler(serviceName)
  };
};

// Service routes with authentication
app.use("/api/user", proxy("http://localhost:5001", createProxyOptions('user-service', 'http://localhost:5001')));
app.use("/api/franchise", proxy("http://localhost:5003", createProxyOptions('franchise-service', 'http://localhost:5003')));
app.use("/api/catalog", proxy("http://localhost:5008", createProxyOptions('account-service', 'http://localhost:5008')));
app.use("/api/vendor", proxy("http://localhost:5009", createProxyOptions('vendor-service', 'http://localhost:5009')));

// 404 handler for unmatched routes
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route not found',
    message: `Cannot ${req.method} ${req.originalUrl}`,
    requestId: req.requestId,
    timestamp: new Date().toISOString()
  });
});

// Error handling middleware
app.use(requestLogger.errorLogger());
app.use(errorHandler.middleware());

// Start server
app.listen(PORT, () => {
  console.log(`🚀 API Gateway running on port ${PORT}`);
  console.log(`📊 Environment: ${NODE_ENV}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/health`);
  console.log(`🔐 JWT Authentication: Disabled`);
});