const Joi = require('joi');

/**
 * Vendor Validation Schema
 * Implements comprehensive validation for vendor data
 */

/**
 * Validate vendor creation data
 */
const validateVendorCreation = (data) => {
  const schema = Joi.object({
    name: Joi.string()
      .trim()
      .max(100)
      .required()
      .messages({
        'string.empty': 'Vendor name is required',
        'string.max': 'Vendor name cannot exceed 100 characters'
      }),
    vendorType: Joi.string()
      .trim()
      .required()
      .messages({
        'string.empty': 'Vendor type is required'
      }),
    city: Joi.string()
      .trim()
      .max(50)
      .required()
      .messages({
        'string.empty': 'City is required',
        'string.max': 'City cannot exceed 50 characters'
      }),
    state: Joi.string()
      .trim()
      .max(50)
      .required()
      .messages({
        'string.empty': 'State is required',
        'string.max': 'State cannot exceed 50 characters'
      }),
    district: Joi.string()
      .trim()
      .max(50)
      .required()
      .messages({
        'string.empty': 'District is required',
        'string.max': 'District cannot exceed 50 characters'
      }),
    pincode: Joi.number()
      .integer()
      .min(100000)
      .max(999999)
      .required()
      .messages({
        'number.min': 'Pincode must be exactly 6 digits',
        'number.max': 'Pincode must be exactly 6 digits',
        'number.base': 'Pincode must be a number',
        'any.required': 'Pincode is required'
      }),
    pan: Joi.string()
      .pattern(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/)
      .optional()
      .messages({
        'string.pattern.base': 'Please enter a valid PAN number'
      }),
    gst_no: Joi.string()
      .pattern(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/)
      .optional()
      .messages({
        'string.pattern.base': 'Please enter a valid GST number'
      }),
    aadharNo: Joi.string()
      .pattern(/^\d{12}$/)
      .optional()
      .messages({
        'string.pattern.base': 'Aadhar number must be exactly 12 digits'
      }),
    contact: Joi.array().items(
      Joi.object({
        name: Joi.string().trim().optional(),
        email: Joi.string().email().optional(),
        mobile: Joi.number().integer().min(1000000000).max(9999999999).optional(),
        designation: Joi.string().trim().optional(),
        isOwner: Joi.boolean().optional()
      })
    ).optional(),
    sourceableBrands: Joi.array().items(
      Joi.object({
        brandId: Joi.string().trim().required(),
        brandName: Joi.string().trim().required()
      })
    ).optional(),
    deleted: Joi.boolean().optional(),
    status: Joi.string()
      .valid('Active', 'Inactive', 'Pending')
      .default('Pending')
      .optional(),
    documents: Joi.array().items(
      Joi.object({
        type: Joi.string().trim().required(),
        refNo: Joi.string().trim().required(),
        assetId: Joi.string().trim().required(),
        status: Joi.string().valid('Active', 'Expired', 'Revoked').default('Active').optional()
      })
    ).optional()
  });

  return schema.validate(data, { abortEarly: false });
};

/**
 * Validate vendor update data
 */
const validateVendorUpdate = (data) => {
  const schema = Joi.object({
    name: Joi.string()
      .trim()
      .max(100)
      .optional()
      .messages({
        'string.max': 'Vendor name cannot exceed 100 characters'
      }),
    vendorType: Joi.string()
      .trim()
      .optional(),
    city: Joi.string()
      .trim()
      .max(50)
      .optional()
      .messages({
        'string.max': 'City cannot exceed 50 characters'
      }),
    state: Joi.string()
      .trim()
      .max(50)
      .optional()
      .messages({
        'string.max': 'State cannot exceed 50 characters'
      }),
    district: Joi.string()
      .trim()
      .max(50)
      .optional()
      .messages({
        'string.max': 'District cannot exceed 50 characters'
      }),
    pincode: Joi.number()
      .integer()
      .min(100000)
      .max(999999)
      .optional()
      .messages({
        'number.min': 'Pincode must be exactly 6 digits',
        'number.max': 'Pincode must be exactly 6 digits',
        'number.base': 'Pincode must be a number'
      }),
    pan: Joi.string()
      .pattern(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/)
      .optional()
      .messages({
        'string.pattern.base': 'Please enter a valid PAN number'
      }),
    gst_no: Joi.string()
      .pattern(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/)
      .optional()
      .messages({
        'string.pattern.base': 'Please enter a valid GST number'
      }),
    aadharNo: Joi.string()
      .pattern(/^\d{12}$/)
      .optional()
      .messages({
        'string.pattern.base': 'Aadhar number must be exactly 12 digits'
      }),
    contact: Joi.array().items(
      Joi.object({
        name: Joi.string().trim().optional(),
        email: Joi.string().email().optional(),
        mobile: Joi.number().integer().min(1000000000).max(9999999999).optional(),
        designation: Joi.string().trim().optional(),
        isOwner: Joi.boolean().optional()
      })
    ).optional(),
    sourceableBrands: Joi.array().items(
      Joi.object({
        brandId: Joi.string().trim().required(),
        brandName: Joi.string().trim().required()
      })
    ).optional(),
    deleted: Joi.boolean().optional(),
    status: Joi.string()
      .valid('Active', 'Inactive', 'Pending')
      .optional(),
    documents: Joi.array().items(
      Joi.object({
        type: Joi.string().trim().required(),
        refNo: Joi.string().trim().required(),
        assetId: Joi.string().trim().required(),
        status: Joi.string().valid('Active', 'Expired', 'Revoked').default('Active').optional()
      })
    ).optional()
  });

  return schema.validate(data, { abortEarly: false });
};

// Middleware functions for Express routes
const validateVendorCreationMiddleware = (req, res, next) => {
  const { error, value } = validateVendorCreation(req.body);

  if (error) {
    const details = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message
    }));

    return res.status(400).json({
      success: false,
      error: 'Validation Error',
      message: 'Invalid input data',
      details,
      requestId: req.requestId || 'unknown',
      timestamp: new Date().toISOString()
    });
  }

  // Store validated data for use in controller
  req.validatedData = value;
  next();
};

const validateVendorUpdateMiddleware = (req, res, next) => {
  const { error, value } = validateVendorUpdate(req.body);

  if (error) {
    const details = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message
    }));

    return res.status(400).json({
      success: false,
      error: 'Validation Error',
      message: 'Invalid input data',
      details,
      requestId: req.requestId || 'unknown',
      timestamp: new Date().toISOString()
    });
  }

  // Store validated data for use in controller
  req.validatedData = value;
  next();
};

module.exports = {
  validateVendorCreation,
  validateVendorUpdate,
  validateVendorCreationMiddleware,
  validateVendorUpdateMiddleware
};