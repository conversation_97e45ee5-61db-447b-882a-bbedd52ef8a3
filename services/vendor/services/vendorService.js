const { Vendor } = require('../models/vendor.model');
const VendorMargin = require('../models/vendorMargin.model');
const { ErrorHandler } = require('../middleware/errorHandler');
const mongoose = require('mongoose');
const ObjectId = mongoose.Types.ObjectId;

/**
 * Create a new vendor
 */
const createVendor = async (vendorData, createdBy) => {
  try {
    // Create vendor locally only
    vendorData.createdBy = createdBy;
    const vendor = new Vendor(vendorData);
    await vendor.save();

    return {
      success: true,
      message: 'Vendor created successfully',
      vendor: vendor
    };
  } catch (error) {
    if (error.code === 11000) {
      // Handle duplicate key error
      const field = Object.keys(error.keyValue)[0];
      throw ErrorHandler.conflict(`Vendor with this ${field} already exists`);
    }
    throw error;
  }
};

/**
 * Get all vendors with pagination and filtering
 */
const getVendors = async (page = 1, limit = 10, filters = {}, outputType = 'list') => {
  try {
    const query = { deleted: false }; // Only get non-deleted vendors

    // Apply legacy filters for backward compatibility
    if (filters.status) {
      query.status = filters.status;
    }
    if (filters.type) {
      query.vendorType = filters.type;
    }
    if (filters.city) {
      query.city = new RegExp(filters.city, 'i');
    }
    if (filters.state) {
      query.state = new RegExp(filters.state, 'i');
    }
    if (filters.search) {
      query.$or = [
        { name: new RegExp(filters.search, 'i') },
        { vendorId: new RegExp(filters.search, 'i') },
        { email: new RegExp(filters.search, 'i') },
        { 'franchise.id': new RegExp(filters.search, 'i') },
        { 'franchise.name': new RegExp(filters.search, 'i') }
      ];
    }

    // Apply new filter object support
    if (filters.filter && typeof filters.filter === 'object') {
      Object.keys(filters.filter).forEach(key => {
        if (key === 'name') {
          query.name = new RegExp(filters.filter[key], 'i');
        } else if (key === 'status') {
          query.status = filters.filter[key];
        } else if (key === 'city') {
          query.city = new RegExp(filters.filter[key], 'i');
        } else if (key === 'state') {
          query.state = new RegExp(filters.filter[key], 'i');
        } else if (key === 'vendorType') {
          query.vendorType = filters.filter[key];
        } else if (key === 'franchise.name') {
          query['franchise.name'] = new RegExp(filters.filter[key], 'i');
        } else if (key === 'franchise.id') {
          query['franchise.id'] = filters.filter[key];
        } else {
          // Direct field matching for other fields
          query[key] = filters.filter[key];
        }
      });
    }

    // If outputType is 'count', return only the count
    if (outputType === 'count') {
      const total = await Vendor.countDocuments(query);
      return {
        success: true,
        message: 'Vendor count retrieved successfully',
        count: total
      };
    }

    // Default behavior: return list with pagination
    const skip = (page - 1) * limit;
    const vendors = await Vendor.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .select('-aadharNo -aadharNoHashKey -auditLog'); // Exclude sensitive fields and audit logs

    const total = await Vendor.countDocuments(query);
    const totalPages = Math.ceil(total / limit);

    return {
      success: true,
      message: 'Vendors retrieved successfully',
      vendors: vendors,
      pagination: {
        currentPage: parseInt(page),
        totalPages: totalPages,
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    };
  } catch (error) {
    throw error;
  }
};



/**
 * Get vendor by ID (supports both MongoDB _id and custom vendorId)
 */
const getVendorById = async (id) => {

  try {
    let vendor;

    vendor = await Vendor.findOne({ _id: id, deleted: false })
      .select('-aadharNo -aadharNoHashKey'); 

    if (!vendor) {
      throw ErrorHandler.notFound('Vendor not found');
    }

    return {
      success: true,
      message: 'Vendor retrieved successfully',
      vendor: vendor
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Update vendor by ID (supports both MongoDB _id and custom vendorId)
 */
const updateVendor = async (id, updateData, modifiedBy = 'SYSTEM') => {
  try {
    // Check if vendor exists - support both ID types
    let existingVendor;
    
    existingVendor = await Vendor.findOne({ _id: id, deleted: false });

    if (!existingVendor) {
      throw ErrorHandler.notFound('Vendor not found');
    }

    // Validate updateData
    if (!updateData || typeof updateData !== 'object') {
      throw ErrorHandler.badRequest('Invalid update data provided');
    }

    // Update fields on the existing document
    Object.keys(updateData).forEach(key => {
      if (key !== '_id' && key !== '__v') {
        existingVendor[key] = updateData[key];
      }
    });

    // Set modification metadata
    existingVendor.modifiedBy = modifiedBy;
    existingVendor.lastUpdated = new Date();

    // Save the document to trigger pre-save middleware for audit logging
    const vendor = await existingVendor.save();

    return {
      success: true,
      message: 'Vendor updated successfully',
      vendor: vendor
    };
  } catch (error) {
    if (error.code === 11000) {
      const field = Object.keys(error.keyValue)[0];
      throw ErrorHandler.conflict(`Vendor with this ${field} already exists`);
    }
    throw error;
  }
};

/**
 * Get vendors by status
 */
const getVendorsByStatus = async (status, page = 1, limit = 10) => {
  try {
    const skip = (page - 1) * limit;

    const vendors = await Vendor.find({ status: status })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .select('-aadharNo -aadharNoHashKey');

    const total = await Vendor.countDocuments({ status: status });
    const totalPages = Math.ceil(total / limit);

    return {
      success: true,
      message: `${status} vendors retrieved successfully`,
      vendors: vendors,
      pagination: {
        currentPage: parseInt(page),
        totalPages: totalPages,
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get vendors by brandId with brand-category margins if applicable
 */
const getVendorsByBrandId = async (brandId, page = 1, limit = 10) => {
  try {
    const skip = (page - 1) * limit;

    // Find all vendor margins that have this brandId (Brand or Brand-Category type)
    const vendorMargins = await VendorMargin.find({
      brandId: brandId,
      type: { $in: ['Brand', 'Brand-Category'] },
      isActive: true,
      deleted: false
    }).select('vendorId type categoryId margin');

    if (vendorMargins.length === 0) {
      return {
        success: true,
        message: 'No vendors found for this brand',
        vendors: [],
        pagination: {
          currentPage: parseInt(page),
          totalPages: 0,
          totalItems: 0,
          itemsPerPage: parseInt(limit),
          hasNextPage: false,
          hasPrevPage: false
        }
      };
    }

    // Extract unique vendor IDs
    const vendorIds = [...new Set(vendorMargins.map(margin => margin.vendorId))];

    // Get vendors with pagination
    // Separate ObjectIds from custom vendorIds
    const objectIds = vendorIds.filter(id => mongoose.Types.ObjectId.isValid(id) && id.length === 24);
    const customVendorIds = vendorIds.filter(id => !mongoose.Types.ObjectId.isValid(id) || id.length !== 24);

    const vendors = await Vendor.find({
      $or: [
        ...(customVendorIds.length > 0 ? [{ vendorId: { $in: customVendorIds } }] : []),
        ...(objectIds.length > 0 ? [{ _id: { $in: objectIds } }] : [])
      ],
      deleted: false
    })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .select('-aadharNo -aadharNoHashKey');

    // Attach margin information to vendors
    const vendorsWithMargins = vendors.map(vendor => {
      const vendorMarginData = vendorMargins.filter(margin =>
        margin.vendorId === vendor.vendorId || margin.vendorId === vendor._id.toString()
      );

      const margins = vendorMarginData.map(margin => ({
        type: margin.type,
        margin: margin.margin,
        categoryId: margin.categoryId || null
      }));

      return {
        ...vendor.toObject(),
        margins: margins
      };
    });

    const total = vendorIds.length;
    const totalPages = Math.ceil(total / limit);

    return {
      success: true,
      message: 'Vendors retrieved successfully by brand ID',
      vendors: vendorsWithMargins,
      pagination: {
        currentPage: parseInt(page),
        totalPages: totalPages,
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get vendors by categoryId with brand-category margins if applicable
 */
const getVendorsByCategoryId = async (categoryId, page = 1, limit = 10) => {
  try {
    const skip = (page - 1) * limit;

    // Find all vendor margins that have this categoryId (Category or Brand-Category type)
    const vendorMargins = await VendorMargin.find({
      categoryId: categoryId,
      type: { $in: ['Category', 'Brand-Category'] },
      isActive: true,
      deleted: false
    }).select('vendorId type brandId margin');

    if (vendorMargins.length === 0) {
      return {
        success: true,
        message: 'No vendors found for this category',
        vendors: [],
        pagination: {
          currentPage: parseInt(page),
          totalPages: 0,
          totalItems: 0,
          itemsPerPage: parseInt(limit),
          hasNextPage: false,
          hasPrevPage: false
        }
      };
    }

    // Extract unique vendor IDs
    const vendorIds = [...new Set(vendorMargins.map(margin => margin.vendorId))];

    // Get vendors with pagination
    // Separate ObjectIds from custom vendorIds
    const objectIds = vendorIds.filter(id => mongoose.Types.ObjectId.isValid(id) && id.length === 24);
    const customVendorIds = vendorIds.filter(id => !mongoose.Types.ObjectId.isValid(id) || id.length !== 24);

    const vendors = await Vendor.find({
      $or: [
        ...(customVendorIds.length > 0 ? [{ vendorId: { $in: customVendorIds } }] : []),
        ...(objectIds.length > 0 ? [{ _id: { $in: objectIds } }] : [])
      ],
      deleted: false
    })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .select('-aadharNo -aadharNoHashKey');

    // Attach margin information to vendors
    const vendorsWithMargins = vendors.map(vendor => {
      const vendorMarginData = vendorMargins.filter(margin =>
        margin.vendorId === vendor.vendorId || margin.vendorId === vendor._id.toString()
      );

      const margins = vendorMarginData.map(margin => ({
        type: margin.type,
        margin: margin.margin,
        brandId: margin.brandId || null
      }));

      return {
        ...vendor.toObject(),
        margins: margins
      };
    });

    const total = vendorIds.length;
    const totalPages = Math.ceil(total / limit);

    return {
      success: true,
      message: 'Vendors retrieved successfully by category ID',
      vendors: vendorsWithMargins,
      pagination: {
        currentPage: parseInt(page),
        totalPages: totalPages,
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get vendors by productId
 */
const getVendorsByProductId = async (productId, page = 1, limit = 10) => {
  try {
    const skip = (page - 1) * limit;

    // Find all vendor margins that have this productId (Product type)
    const vendorMargins = await VendorMargin.find({
      productId: productId,
      type: 'Product',
      isActive: true,
      deleted: false
    }).select('vendorId margin');

    if (vendorMargins.length === 0) {
      return {
        success: true,
        message: 'No vendors found for this product',
        vendors: [],
        pagination: {
          currentPage: parseInt(page),
          totalPages: 0,
          totalItems: 0,
          itemsPerPage: parseInt(limit),
          hasNextPage: false,
          hasPrevPage: false
        }
      };
    }

    // Extract unique vendor IDs
    const vendorIds = [...new Set(vendorMargins.map(margin => margin.vendorId))];

    // Get vendors with pagination
    // Separate ObjectIds from custom vendorIds
    const objectIds = vendorIds.filter(id => mongoose.Types.ObjectId.isValid(id) && id.length === 24);
    const customVendorIds = vendorIds.filter(id => !mongoose.Types.ObjectId.isValid(id) || id.length !== 24);

    const vendors = await Vendor.find({
      $or: [
        ...(customVendorIds.length > 0 ? [{ vendorId: { $in: customVendorIds } }] : []),
        ...(objectIds.length > 0 ? [{ _id: { $in: objectIds } }] : [])
      ],
      deleted: false
    })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .select('-aadharNo -aadharNoHashKey');

    // Attach margin information to vendors
    const vendorsWithMargins = vendors.map(vendor => {
      const vendorMarginData = vendorMargins.filter(margin =>
        margin.vendorId === vendor.vendorId || margin.vendorId === vendor._id.toString()
      );

      const margins = vendorMarginData.map(margin => ({
        type: 'Product',
        margin: margin.margin
      }));

      return {
        ...vendor.toObject(),
        margins: margins
      };
    });

    const total = vendorIds.length;
    const totalPages = Math.ceil(total / limit);

    return {
      success: true,
      message: 'Vendors retrieved successfully by product ID',
      vendors: vendorsWithMargins,
      pagination: {
        currentPage: parseInt(page),
        totalPages: totalPages,
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Update vendor status (supports both MongoDB _id and custom vendorId)
 */
const updateVendorStatus = async (id, status, userId, remarks = '') => {
  try {
    let vendor;

    // Check if the ID looks like a MongoDB ObjectId (24 hex characters)
    if (id.match(/^[0-9a-fA-F]{24}$/)) {
      // Use MongoDB _id
      vendor = await Vendor.findById(id);
    } else {
      // Use custom vendorId (like **********)
      vendor = await Vendor.findOne({ vendorId: id });
    }

    if (!vendor) {
      throw ErrorHandler.notFound('Vendor not found');
    }

    // Update vendor status on the existing document
    vendor.status = status;
    vendor.modifiedBy = userId;
    vendor.lastUpdated = new Date();

    // Save the document to trigger pre-save middleware for audit logging
    const updatedVendor = await vendor.save();

    return {
      success: true,
      message: 'Vendor status updated successfully',
      vendor: updatedVendor
    };
  } catch (error) {
    throw error;
  }
};

module.exports = {
  createVendor,
  getVendors,
  getVendorById,
  updateVendor,
  getVendorsByStatus,
  getVendorsByBrandId,
  getVendorsByCategoryId,
  getVendorsByProductId,
  updateVendorStatus
};