const { Vendor, fetchVendorFromExternalAPI } = require('../models/vendor.model');
const VendorMargin = require('../models/vendorMargin.model');
const { ErrorHandler } = require('../middleware/errorHandler');
const axios = require('axios');

/**
 * Vendor Service
 * Handles vendor operations including external API integration
 */

/**
 * Transform external vendor data to our internal format
 */
const transformExternalVendorData = (externalData) => {
    return {
        vendorId: externalData._id,
        name: externalData.name,
        vendorType: externalData.vendorType || 'Others',
        city: externalData.city,
        state: externalData.state,
        district: externalData.district,
        pincode: externalData.pincode,
        pan: externalData.pan,
        gst_no: externalData.gst_no,
        aadharNo: externalData.aadharNo,
        aadharNoHashKey: externalData.aadharNoHashKey,
        status: externalData.status || 'Active',
        contact: externalData.contact ? externalData.contact.map(c => ({
            name: c.name,
            email: c.email,
            mobile: c.mobile,
            designation: c.designation,
            isOwner: c.isOwner === 'true' || c.isOwner === true
        })) : [],
        documents: externalData.docs ? externalData.docs.map(doc => ({
            type: doc.type,
            refNo: doc.name,
            assetId: doc.value
        })) : [],
        sourceableBrands: [], // Will be populated separately if needed
        deleted: externalData.deleted || false,
        createdBy: externalData.createdBy,
        createdAt: externalData.createdAt ? new Date(externalData.createdAt) : new Date(),
        lastUpdated: externalData.lastUpdated ? new Date(externalData.lastUpdated) : new Date()
    };
};

/**
 * Create a new vendor
 */
const createVendor = async (vendorData, createdBy, userToken = null) => {
  try {
    if (userToken) {
      // If token provided, create vendor in external API first to get vendorId
      const externalResponse = await axios.post(`${process.env.EXTERNAL_API_BASE_URL}/vendor/v1`, vendorData, {
          headers: {
              'Authorization': `Bearer ${userToken}`,
              'Content-Type': 'application/json'
          }
      });

      // Use the _id from external API as our vendorId
      const vendorId = externalResponse.data._id;

      // Transform and save to our local database
      const transformedData = transformExternalVendorData(externalResponse.data);
      transformedData.createdBy = createdBy;

      const vendor = new Vendor(transformedData);
      await vendor.save();

      return {
        success: true,
        message: 'Vendor created successfully via external API',
        vendor: vendor
      };
    } else {
      // If no token, create vendor locally only
      vendorData.createdBy = createdBy;

      const vendor = new Vendor(vendorData);
      await vendor.save();

      return {
        success: true,
        message: 'Vendor created successfully in local database',
        vendor: vendor
      };
    }
  } catch (error) {
    if (error.code === 11000) {
      // Handle duplicate key error
      const field = Object.keys(error.keyValue)[0];
      throw ErrorHandler.conflict(`Vendor with this ${field} already exists`);
    }
    throw error;
  }
};

/**
 * Get all vendors with pagination and filtering
 */
const getVendors = async (page = 1, limit = 10, filters = {}) => {
  try {
    const skip = (page - 1) * limit;
    const query = {};

    // Apply filters
    if (filters.status) {
      query.status = filters.status;
    }
    if (filters.type) {
      query.vendorType = filters.type;
    }
    if (filters.city) {
      query.city = new RegExp(filters.city, 'i');
    }
    if (filters.state) {
      query.state = new RegExp(filters.state, 'i');
    }
    if (filters.search) {
      query.$or = [
        { name: new RegExp(filters.search, 'i') },
        { vendorId: new RegExp(filters.search, 'i') },
        { email: new RegExp(filters.search, 'i') },
        { franchiseId: new RegExp(filters.search, 'i') },
        { franchiseName: new RegExp(filters.search, 'i') }
      ];
    }

    //dont include auditLog, createdBy, createdOn, modifiedBy, modifiedOn

    const vendors = await Vendor.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .select('-aadharNo -aadharNoHashKey'); // Exclude sensitive fields

    const total = await Vendor.countDocuments(query);
    const totalPages = Math.ceil(total / limit);

    return {
      success: true,
      message: 'Vendors retrieved successfully',
      vendors: vendors,
      pagination: {
        currentPage: parseInt(page),
        totalPages: totalPages,
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Transform external margin data to our internal format
 */
const transformExternalMarginData = (vendorId, marginData) => {
    const marginType = marginData.marginType || 'Brand'; // Default to Brand if not specified

    const baseMargin = {
        vendorId: vendorId,
        type: marginType,
        margin: marginData.margin,
        startDate: marginData.start_date ? new Date(marginData.start_date) : new Date(),
        endDate: marginData.end_date ? new Date(marginData.end_date) : null,
        sourceAllBrands: false,
        isActive: true
    };

    // Add specific fields based on margin type
    if (marginType === 'Brand' && marginData.brand) {
        baseMargin.brandId = marginData.brand;
    } else if (marginType === 'Category' && marginData.category) {
        baseMargin.categoryId = marginData.category;
    } else if (marginType === 'Product' && marginData.product) {
        baseMargin.productId = marginData.product;
    }

    return baseMargin;
};

/**
 * Process and save vendor margins from external data
 */
const processVendorMargins = async (vendorId, marginsData) => {
    try {
        for (const marginData of marginsData) {
            const transformedMargin = transformExternalMarginData(vendorId, marginData);

            // Check if margin already exists
            const existingMargin = await VendorMargin.findOne({
                vendorId: vendorId,
                type: transformedMargin.type,
                brandId: transformedMargin.brandId,
                categoryId: transformedMargin.categoryId,
                productId: transformedMargin.productId,
                isActive: true
            });

            if (!existingMargin) {
                const margin = new VendorMargin(transformedMargin);
                await margin.save();
                console.log(`Saved margin for vendor ${vendorId}, type: ${transformedMargin.type}`);
            }
        }
    } catch (error) {
        console.error('Error processing vendor margins:', error.message);
        // Don't throw error here as vendor creation should succeed even if margins fail
    }
};

/**
 * Get vendor by ID - checks local first, then external API if token provided
 */
const getVendorById = async (id, userToken = null) => {
  try {
    // First check local database (no token required)
    let vendor = await Vendor.findOne({ vendorId: id, deleted: false })
        .select('-aadharNo -aadharNoHashKey'); // Exclude sensitive fields

    if (vendor) {
        return {
            success: true,
            message: 'Vendor retrieved successfully',
            vendor: vendor
        };
    }

    // If not found locally, check if we have a token to fetch from external API
    if (!userToken) {
        throw ErrorHandler.notFound('Vendor not found in local database. Please provide authorization token to fetch from external API.');
    }

    // If token is provided, fetch from external API
    console.log(`Vendor ${id} not found locally, fetching from external API...`);

    const externalData = await fetchVendorFromExternalAPI(id, userToken);

    if (!externalData) {
        throw ErrorHandler.notFound('Vendor not found in external API');
    }

    // Transform external data to our format
    const transformedData = transformExternalVendorData(externalData);

    // Save to local database
    vendor = new Vendor(transformedData);
    await vendor.save();

    // Process margins if they exist in external data
    if (externalData.margins && externalData.margins.length > 0) {
        await processVendorMargins(id, externalData.margins);
    }

    return {
        success: true,
        message: 'Vendor retrieved from external API and saved locally',
        vendor: vendor
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Update vendor by ID (supports both MongoDB _id and custom vendorId)
 */
const updateVendor = async (id, updateData, modifiedBy = 'SYSTEM') => {
  try {
    // Check if vendor exists - support both ID types
    let existingVendor;
    if (id.match(/^[0-9a-fA-F]{24}$/)) {
      existingVendor = await Vendor.findById(id);
    } else {
      existingVendor = await Vendor.findOne({ vendorId: id });
    }

    if (!existingVendor) {
      throw ErrorHandler.notFound('Vendor not found');
    }

    // Validate updateData
    if (!updateData || typeof updateData !== 'object') {
      throw ErrorHandler.badRequest('Invalid update data provided');
    }

    // Update fields on the existing document
    Object.keys(updateData).forEach(key => {
      if (key !== '_id' && key !== '__v') {
        existingVendor[key] = updateData[key];
      }
    });

    // Set modification metadata
    existingVendor.modifiedBy = modifiedBy;
    existingVendor.lastUpdated = new Date();

    // Save the document to trigger pre-save middleware for audit logging
    const vendor = await existingVendor.save();

    return {
      success: true,
      message: 'Vendor updated successfully',
      vendor: vendor
    };
  } catch (error) {
    if (error.code === 11000) {
      const field = Object.keys(error.keyValue)[0];
      throw ErrorHandler.conflict(`Vendor with this ${field} already exists`);
    }
    throw error;
  }
};

/**
 * Delete vendor by ID (supports both MongoDB _id and custom vendorId)
 */
const deleteVendor = async (id) => {
  try {
    let vendor;

    // Check if the ID looks like a MongoDB ObjectId (24 hex characters)
    if (id.match(/^[0-9a-fA-F]{24}$/)) {
      // Use MongoDB _id
      vendor = await Vendor.findByIdAndDelete(id);
    } else {
      // Use custom vendorId (like **********)
      vendor = await Vendor.findOneAndDelete({ vendorId: id });
    }

    if (!vendor) {
      throw ErrorHandler.notFound('Vendor not found');
    }

    return {
      success: true,
      message: 'Vendor deleted successfully',
      vendor: vendor
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get vendors by status
 */
const getVendorsByStatus = async (status, page = 1, limit = 10) => {
  try {
    const skip = (page - 1) * limit;

    const vendors = await Vendor.find({ status: status })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .select('-aadharNo -aadharNoHashKey');

    const total = await Vendor.countDocuments({ status: status });
    const totalPages = Math.ceil(total / limit);

    return {
      success: true,
      message: `${status} vendors retrieved successfully`,
      vendors: vendors,
      pagination: {
        currentPage: parseInt(page),
        totalPages: totalPages,
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Update vendor status (supports both MongoDB _id and custom vendorId)
 */
const updateVendorStatus = async (id, status, userId, remarks = '') => {
  try {
    let vendor;

    // Check if the ID looks like a MongoDB ObjectId (24 hex characters)
    if (id.match(/^[0-9a-fA-F]{24}$/)) {
      // Use MongoDB _id
      vendor = await Vendor.findById(id);
    } else {
      // Use custom vendorId (like **********)
      vendor = await Vendor.findOne({ vendorId: id });
    }

    if (!vendor) {
      throw ErrorHandler.notFound('Vendor not found');
    }

    // Update vendor status on the existing document
    vendor.status = status;
    vendor.modifiedBy = userId;
    vendor.lastUpdated = new Date();

    // Save the document to trigger pre-save middleware for audit logging
    const updatedVendor = await vendor.save();

    return {
      success: true,
      message: 'Vendor status updated successfully',
      vendor: updatedVendor
    };
  } catch (error) {
    throw error;
  }
};

module.exports = {
  createVendor,
  getVendors,
  getVendorById,
  updateVendor,
  deleteVendor,
  getVendorsByStatus,
  updateVendorStatus,
  transformExternalVendorData,
  transformExternalMarginData,
  processVendorMargins
};