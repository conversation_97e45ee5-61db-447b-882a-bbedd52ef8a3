const Vendor = require('../models/vendor.model');
const { <PERSON><PERSON>r<PERSON>and<PERSON> } = require('../middleware/errorHandler');

/**
 * Vendor Service
 * Handles business logic for vendor management operations
 */

/**
 * Create a new vendor
 */
const createVendor = async (vendorData) => {
  try {
    // Create new vendor
    const vendor = new Vendor(vendorData);
    await vendor.save();

    return {
      success: true,
      message: 'Vendor created successfully',
      vendor: vendor
    };
  } catch (error) {
    if (error.code === 11000) {
      // Handle duplicate key error
      const field = Object.keys(error.keyValue)[0];
      throw ErrorHandler.conflict(`Vendor with this ${field} already exists`);
    }
    throw error;
  }
};

/**
 * Get all vendors with pagination and filtering
 */
const getVendors = async (page = 1, limit = 10, filters = {}) => {
  try {
    const skip = (page - 1) * limit;
    const query = {};

    // Apply filters
    if (filters.status) {
      query.status = filters.status;
    }
    if (filters.type) {
      query.vendorType = filters.type;
    }
    if (filters.city) {
      query.city = new RegExp(filters.city, 'i');
    }
    if (filters.state) {
      query.state = new RegExp(filters.state, 'i');
    }
    if (filters.search) {
      query.$or = [
        { name: new RegExp(filters.search, 'i') },
        { vendorId: new RegExp(filters.search, 'i') },
        { email: new RegExp(filters.search, 'i') },
        { franchiseId: new RegExp(filters.search, 'i') },
        { franchiseName: new RegExp(filters.search, 'i') }
      ];
    }

    //dont include auditLog, createdBy, createdOn, modifiedBy, modifiedOn

    const vendors = await Vendor.find(query)
      .sort({ createdOn: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .select('-aadharNo -aadharNoHashKey'); // Exclude sensitive fields

    const total = await Vendor.countDocuments(query);
    const totalPages = Math.ceil(total / limit);

    return {
      success: true,
      message: 'Vendors retrieved successfully',
      vendors: vendors,
      pagination: {
        currentPage: parseInt(page),
        totalPages: totalPages,
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get vendor by ID (supports both MongoDB _id and custom vendorId)
 */
const getVendorById = async (id) => {
  try {
    let vendor;

    // Check if the ID looks like a MongoDB ObjectId (24 hex characters)
    if (id.match(/^[0-9a-fA-F]{24}$/)) {
      // Use MongoDB _id
      vendor = await Vendor.findById(id)
        .select('-aadharNo -aadharNoHashKey'); // Exclude sensitive fields
    } else {
      // Use custom vendorId (like **********)
      vendor = await Vendor.findOne({ vendorId: id })
        .select('-aadharNo -aadharNoHashKey'); // Exclude sensitive fields
    }

    if (!vendor) {
      throw ErrorHandler.notFound('Vendor not found');
    }

    return {
      success: true,
      message: 'Vendor retrieved successfully',
      vendor: vendor
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Update vendor by ID (supports both MongoDB _id and custom vendorId)
 */
const updateVendor = async (id, updateData, modifiedBy = 'SYSTEM') => {
  try {
    // Check if vendor exists - support both ID types
    let existingVendor;
    if (id.match(/^[0-9a-fA-F]{24}$/)) {
      existingVendor = await Vendor.findById(id);
    } else {
      existingVendor = await Vendor.findOne({ vendorId: id });
    }

    if (!existingVendor) {
      throw ErrorHandler.notFound('Vendor not found');
    }

    // Validate updateData
    if (!updateData || typeof updateData !== 'object') {
      throw ErrorHandler.badRequest('Invalid update data provided');
    }

    // Update fields on the existing document
    Object.keys(updateData).forEach(key => {
      if (key !== '_id' && key !== '__v') {
        existingVendor[key] = updateData[key];
      }
    });

    // Set modification metadata
    existingVendor.modifiedBy = modifiedBy;
    existingVendor.modifiedOn = new Date();

    // Save the document to trigger pre-save middleware for audit logging
    const vendor = await existingVendor.save();

    return {
      success: true,
      message: 'Vendor updated successfully',
      vendor: vendor
    };
  } catch (error) {
    if (error.code === 11000) {
      const field = Object.keys(error.keyValue)[0];
      throw ErrorHandler.conflict(`Vendor with this ${field} already exists`);
    }
    throw error;
  }
};

/**
 * Delete vendor by ID (supports both MongoDB _id and custom vendorId)
 */
const deleteVendor = async (id) => {
  try {
    let vendor;

    // Check if the ID looks like a MongoDB ObjectId (24 hex characters)
    if (id.match(/^[0-9a-fA-F]{24}$/)) {
      // Use MongoDB _id
      vendor = await Vendor.findByIdAndDelete(id);
    } else {
      // Use custom vendorId (like **********)
      vendor = await Vendor.findOneAndDelete({ vendorId: id });
    }

    if (!vendor) {
      throw ErrorHandler.notFound('Vendor not found');
    }

    return {
      success: true,
      message: 'Vendor deleted successfully',
      vendor: vendor
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get vendors by status
 */
const getVendorsByStatus = async (status, page = 1, limit = 10) => {
  try {
    const skip = (page - 1) * limit;

    const vendors = await Vendor.find({ status: status })
      .sort({ createdOn: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .select('-aadharNo -aadharNoHashKey');

    const total = await Vendor.countDocuments({ status: status });
    const totalPages = Math.ceil(total / limit);

    return {
      success: true,
      message: `${status} vendors retrieved successfully`,
      vendors: vendors,
      pagination: {
        currentPage: parseInt(page),
        totalPages: totalPages,
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Update vendor status (supports both MongoDB _id and custom vendorId)
 */
const updateVendorStatus = async (id, status, userId, remarks = '') => {
  try {
    let vendor;

    // Check if the ID looks like a MongoDB ObjectId (24 hex characters)
    if (id.match(/^[0-9a-fA-F]{24}$/)) {
      // Use MongoDB _id
      vendor = await Vendor.findById(id);
    } else {
      // Use custom vendorId (like **********)
      vendor = await Vendor.findOne({ vendorId: id });
    }

    if (!vendor) {
      throw ErrorHandler.notFound('Vendor not found');
    }

    // Update vendor status on the existing document
    vendor.status = status;
    vendor.modifiedBy = userId;
    vendor.modifiedOn = new Date();

    // Save the document to trigger pre-save middleware for audit logging
    const updatedVendor = await vendor.save();

    return {
      success: true,
      message: 'Vendor status updated successfully',
      vendor: updatedVendor
    };
  } catch (error) {
    throw error;
  }
};

module.exports = {
  createVendor,
  getVendors,
  getVendorById,
  updateVendor,
  deleteVendor,
  getVendorsByStatus,
  updateVendorStatus
};