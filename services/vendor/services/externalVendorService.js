const axios = require('axios');
const Vendor = require('../models/vendor.model');
const { <PERSON>rror<PERSON>andler } = require('../middleware/errorHandler');

const EXTERNAL_VENDOR_SERVICE_URL = process.env.EXTERNAL_VENDOR_SERVICE_URL || 'https://uat.storeking.in/apigateway/api';

/**
 * External Vendor Service
 * Handles fetching vendor data from external service and storing in local DB
 */

/**
 * Helper function to map external vendor data to our vendor model
 */
function mapExternalVendorData(externalVendor) {
  // Map external vendor structure to our vendor model
  const contact = externalVendor.contact_details && externalVendor.contact_details[0] 
    ? [{
        name: externalVendor.contact_details[0].name || '',
        mobileNo: externalVendor.contact_details[0].mobile || externalVendor.whatsAppNumber || externalVendor.username || '',
        designation: externalVendor.contact_details[0].designation || 'Owner',
        isOwner: true
      }]
    : [];

  const documents = [];
  
  // Map documents from external service
  if (externalVendor.documents) {
    externalVendor.documents.forEach(doc => {
      documents.push({
        type: doc.type || 'Unknown',
        refNo: doc.refNo || doc.refNumber || '',
        assetId: doc.assetId || doc.id || '',
        status: doc.status === 'Active' ? 'Active' : 'Inactive'
      });
    });
  }

  return {
    name: externalVendor.name || '',
    vendorType: externalVendor.vendorType || externalVendor.type || 'Individual',
    city: externalVendor.city || externalVendor.town || '',
    state: externalVendor.state || '',
    district: externalVendor.district || '',
    contact: contact,
    pincode: String(externalVendor.pincode || ''),
    panNo: externalVendor.panNo || externalVendor.finance_details?.pan_no || '',
    gstNo: externalVendor.gstNo || externalVendor.finance_details?.gstNo || '',
    mobileNo: contact.length > 0 ? contact[0].mobileNo : '',
    email: contact.length > 0 ? (externalVendor.contact_details?.[0]?.email || '') : '',
    status: externalVendor.status === 'Active' ? 'Active' : 'Pending',
    documents: documents,
    auditLog: [],
    createdBy: 'EXTERNAL_SYNC',
    createdOn: externalVendor.createdAt ? new Date(externalVendor.createdAt) : new Date(),
    modifiedBy: 'EXTERNAL_SYNC',
    modifiedOn: externalVendor.updatedAt ? new Date(externalVendor.updatedAt) : new Date()
  };
}

/**
 * Fetch vendor data from external service using user's token
 */
async function fetchVendorFromExternalService(vendorId, userToken) {
  try {
    console.log(`Fetching vendor ${vendorId} from external service...`);
    console.log(`External service URL: ${EXTERNAL_VENDOR_SERVICE_URL}/vendor/v1/${vendorId}`);
    console.log(`Using token: ${userToken ? 'JWT ' + userToken.substring(0, 20) + '...' : 'No token'}`);

    const response = await axios.get(`${EXTERNAL_VENDOR_SERVICE_URL}/vendor/v1/${vendorId}`, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `JWT ${userToken}`
      },
      timeout: 30000
    });

    if (!response.data) {
      throw ErrorHandler.notFound(`Vendor ${vendorId} not found in external service`);
    }

    return response.data;
  } catch (error) {
    if (error.response?.status === 404) {
      throw ErrorHandler.notFound(`Vendor ${vendorId} not found in external service`);
    }
    
    const statusCode = error.response?.status || 500;
    const message = error.response?.data?.message || 'Failed to fetch vendor from external service';
    
    console.error(`Error fetching vendor ${vendorId} from external service:`, {
      status: statusCode,
      message: message,
      error: error.message
    });
    
    throw ErrorHandler.internal(`External service error: ${message}`);
  }
}

/**
 * Save external vendor data to local database
 */
async function saveExternalVendorToLocal(externalVendorData) {
  try {
    const mappedVendorData = mapExternalVendorData(externalVendorData);
    
    // Create new vendor in local database
    const vendor = new Vendor(mappedVendorData);
    await vendor.save();
    
    console.log(`Successfully saved external vendor to local DB with vendorId: ${vendor.vendorId}`);
    
    return vendor;
  } catch (error) {
    console.error('Error saving external vendor to local DB:', error);
    throw ErrorHandler.internal('Failed to save vendor data to local database');
  }
}

/**
 * Check if vendor exists locally, if not fetch from external service and store
 * Returns the vendor's MongoDB _id for use as vendorId in vendor margins
 */
async function ensureVendorExists(vendorId, userToken) {
  try {
    // First check if vendor exists in local database
    let existingVendor = await Vendor.findOne({ vendorId: vendorId });
    
    if (existingVendor) {
      console.log(`Vendor ${vendorId} found in local database`);
      return existingVendor._id.toString(); // Return MongoDB _id
    }

    // If not found locally and no user token provided, throw error
    if (!userToken) {
      throw ErrorHandler.badRequest('Vendor not found locally and no authentication token provided to fetch from external service');
    }

    console.log(`Vendor ${vendorId} not found locally, fetching from external service...`);
    
    // Fetch from external service
    const externalVendorData = await fetchVendorFromExternalService(vendorId, userToken);
    
    // Save to local database
    const savedVendor = await saveExternalVendorToLocal(externalVendorData);
    
    console.log(`Vendor ${vendorId} successfully fetched and saved. Local _id: ${savedVendor._id}`);
    
    return savedVendor._id.toString(); // Return MongoDB _id
    
  } catch (error) {
    console.error(`Error ensuring vendor ${vendorId} exists:`, error);
    throw error;
  }
}

module.exports = {
  fetchVendorFromExternalService,
  saveExternalVendorToLocal,
  ensureVendorExists,
  mapExternalVendorData
};
