const mongoose = require('mongoose');


/**
 * Generate vendorId in order, form VEN1 to VEN99999999, if vendorId is not provided
 */
const generateVendorId = async (Model) => {
    // Find vendors with valid vendorId format (VEN followed by numbers)
    const lastVendor = await Model.findOne({
        vendorId: {
            $exists: true,
            $ne: null,
            $regex: /^VEN\d+$/
        }
    }, { vendorId: 1, _id: 0 })
        .sort({ vendorId: -1 })
        .limit(1);

    if (lastVendor && lastVendor.vendorId) {
        try {
            const lastId = parseInt(lastVendor.vendorId.substring(3));
            if (!isNaN(lastId)) {
                const nextId = lastId + 1;
                return `VEN${nextId}`;
            }
        } catch (error) {
            console.log('Error parsing vendorId, starting from VEN1:', error);
        }
    }

    return `VEN1`;
};



/**
 * Vendor Schema for Vendor Management System
 * Implements comprehensive vendor data structure with embedded documents
*/
const vendorSchema = new mongoose.Schema({
    vendorId: {
        type: String,
        unique: true
    },
    name: {
        type: String,
        required: true,
        trim: true,
        maxlength: 100
    },
    vendorType: {
        type: String,
        required: true
    },
    city: {
        type: String,
        required: true,
        trim: true,
        maxlength: 50
    },
    state: {
        type: String,
        required: true,
        trim: true,
        maxlength: 50
    },
    district: {
        type: String,
        required: true,
        trim: true,
        maxlength: 50
    },
    contact: [{
        name: String,
        email: String,
        mobile: Number,
        designation: String,
        isOwner: Boolean
    }],
    pincode: {
        type: Number,
        required: true,
        trim: true,
        match: [/^\d{6}$/, 'Pincode must be exactly 6 digits']
    },
    pan: {
        type: String,
        trim: true,
        // match: [/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/, 'Please enter a valid PAN number']
    },
    gst_no: {
        type: String,
        trim: true,
        // match: [/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/, 'Please enter a valid GST number']
    },
    aadharNo: {
        type: String,
        trim: true,
        // match: [/^\d{12}$/, 'Aadhar number must be exactly 12 digits']
    },
    aadharNoHashKey: {
        type: String,
        trim: true
    },
    status: {
        type: String,
        required: true,
        enum: ['Active', 'Inactive', 'Pending'],
        default: 'Pending'
    },
    franchise: {
        id: {
            type: String
        },
        name: {
            type: String
        }
    },
    sourceableBrands: [{
        brandId: {
            type: String,
            required: true,
            trim: true
        },
        brandName: {
            type: String,
            required: true,
            trim: true
        }
    }],
    documents: {
        type: [{
            type: {
                type: String,
                required: true,
                trim: true
            },
            refNo: {
                type: String,
                required: true,
                trim: true
            },
            assetId: {
                type: String,
                required: true,
                trim: true
            },
            status: {
                type: String,
                enum: ['Active', 'Expired', 'Revoked'],
                default: 'Active'
            },
            verified: {
                id: {
                    type: String,
                    ref: 'User'
                },
                on: {
                    type: Date
                }
            }
        }],
        default: []
    },
    auditLog: [
        {
            status: { type: String },
            createdAt: Date,
            createdBy: String,
            remarks: String
        }
    ],
    createdBy: {
        type: String,
        ref: 'User'
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    modifiedBy: {
        type: String,
        ref: 'User'
    },
    lastUpdated: {
        type: Date,
        default: Date.now
    },
    deleted: {
        type: Boolean,
        default: false
    }
});

// Generate vendorId for new vendors
vendorSchema.pre('save', async function (next) {
    if (this.isNew && !this.vendorId) {
        try {
            this.vendorId = await generateVendorId(this.constructor);
        } catch (error) {
            return next(error);
        }
    }
    next();
});

// Store original values before any modifications for audit logging
vendorSchema.pre('save', async function (next) {
    if (!this.isNew && this.isModified()) {
        // Get the original document from database
        try {
            const original = await this.constructor.findById(this._id).lean();
            this.$__originalDoc = original;
        } catch (error) {
            // If we can't get original, continue without detailed change tracking
            console.warn('Could not fetch original document for audit logging:', error.message);
        }
    }
    next();
});

// Pre-save middleware to update lastUpdated and add comprehensive audit log
vendorSchema.pre('save', function (next) {
    const now = new Date();

    if (this.isModified() && !this.isNew) {
        this.lastUpdated = now;

        // Track changes and create audit log entry
        const changes = [];
        const modifiedPaths = this.modifiedPaths();

        // Exclude audit log and system fields from change tracking
        const excludeFields = ['auditLog', 'lastUpdated', 'modifiedBy', '__v'];
        const relevantChanges = modifiedPaths.filter(path => !excludeFields.includes(path));

        if (relevantChanges.length > 0 && this.$__originalDoc) {
            relevantChanges.forEach(path => {
                const originalValue = this.$__originalDoc[path];
                const newValue = this[path];

                // Handle nested objects (like address)
                if (typeof originalValue === 'object' && typeof newValue === 'object' && originalValue !== null && newValue !== null) {
                    // For nested objects, compare JSON strings
                    if (JSON.stringify(originalValue) !== JSON.stringify(newValue)) {
                        changes.push(`${path} updated`);
                    }
                } else if (originalValue !== newValue) {
                    // Handle primitive values
                    const oldVal = originalValue === null || originalValue === undefined ? 'null' : originalValue;
                    const newVal = newValue === null || newValue === undefined ? 'null' : newValue;
                    changes.push(`${path} changed from "${oldVal}" to "${newVal}"`);
                }
            });

            let remarks;
            if (changes.length === 0) {
                // No actual changes detected
                return next();
            } else if (changes.length === 1) {
                remarks = changes[0];
            } else if (changes.length <= 3) {
                remarks = changes.join(', ');
            } else {
                // If too many changes, just list the field names
                remarks = `Multiple fields updated: ${relevantChanges.join(', ')}`;
            }

            // Add audit log entry
            const auditEntry = {
                status: 'UPDATED',
                createdAt: now,
                createdBy: this.modifiedBy || 'SYSTEM',
                remarks: remarks
            };

            this.auditLog.push(auditEntry);
        } else if (relevantChanges.length > 0) {
            // Fallback when original document is not available
            const auditEntry = {
                status: 'UPDATED',
                createdOn: now,
                createdBy: this.modifiedBy || 'SYSTEM',
                remarks: `Fields updated: ${relevantChanges.join(', ')}`
            };

            this.auditLog.push(auditEntry);
        }
    }

    next();
});


const Vendor = mongoose.model('Vendor', vendorSchema);

module.exports = {
    Vendor
};