const mongoose = require('mongoose');

/**
 * Generate vendorId in order, form VE00000001 to VE99999999, if vendorId is not provided
 */

const generateVendorId = async (Model) => {
    const lastVendor = await Model.findOne({}, { vendorId: 1, _id: 0 })
        .sort({ vendorId: -1 })
        .limit(1);
    if (lastVendor) {
        const lastId = parseInt(lastVendor.vendorId.substring(2));
        const nextId = lastId + 1;
        return `VE${nextId.toString().padStart(8, '0')}`;
    } else {
        return `VE00000001`;
    }
};

/**
 * Vendor Schema for Vendor Management System
 * Implements comprehensive vendor data structure with embedded documents
*/
const vendorSchema = new mongoose.Schema({
    vendorId:{
        type: String,
        unique: true,
        index: true
    },
    name: {
        type: String,
        required: true,
        trim: true,
        maxlength: 100,
        index: true
    },
    vendorType: {
        type: String,
        required: true,
        index: true
    },
    city: {
        type: String,
        required: true,
        trim: true,
        maxlength: 50,
        index: true
    },
    state: {
        type: String,
        required: true,
        trim: true,
        maxlength: 50,
        index: true
    },
    district: {
        type: String,
        required: true,
        trim: true,
        maxlength: 50,  
        index: true
    },
    contact:[{
        name: String,
        mobileNo: String,
        designation: String,
        isOwner: Boolean
    }],
    pincode: {
        type: Number,
        required: true,
        trim: true,
        match: [/^\d{6}$/, 'Pincode must be exactly 6 digits']
    },
    panNo: {
        type: String,
        trim: true,
        // match: [/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/, 'Please enter a valid PAN number']
    },
    gstNo: {
        type: String,
        trim: true,
        // match: [/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/, 'Please enter a valid GST number']
    },
    aadharNo: {
        type: String,
        trim: true,
        // match: [/^\d{12}$/, 'Aadhar number must be exactly 12 digits']
    },
    aadharNoHashKey: {
        type: String,
        trim: true
    },
    mobileNo: {
        type: String,
        trim: true,
        match: [/^\d{10}$/, 'Mobile number must be exactly 10 digits']
    },
    email: {
        type: String,
        trim: true,
        lowercase: true,
        match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
    },
    status: {
        type: String,
        required: true,
        enum: ['Active', 'Inactive', 'Pending'],
        default: 'Pending',
        index: true
    },
    franchiseId: {
        type: String,
        index: true
    },
    franchiseName: {
        type: String,
        index: true
    },
    documents: {
        type: [{
            type: {
                type: String,
                required: true,
                trim: true
            },
            refNo: {
                type: String,
                required: true,
                trim: true
            },
            assetId: {
                type: String,
                required: true,
                trim: true
            },
            status: {
                type: String,
                enum: ['Active', 'Expired', 'Revoked'],
                default: 'Active'
            },
            verified: {
                id: {
                    type: String,
                    ref: 'User'
                },
                on: {
                    type: Date
                }
            }
        }],
        default: []
    },
    auditLog:[
        {
            status: {type: String},
            createdOn: Date,
            createdBy: String,
            remarks: String
        }
    ],
    createdBy: {
        type: String,
        ref: 'User',
        index: true
    },
    createdOn: {
        type: Date,
        default: Date.now,
        index: true
    },
    modifiedBy: {
        type: String,
        ref: 'User',
    },
    modifiedOn: {
        type: Date,
        default: Date.now
    }
});

//add prehook for id generation

vendorSchema.pre('save', async function(next) {
    if (!this.vendorId) {
        try {
            this.vendorId = await generateVendorId(this.constructor); // avoids circular model lookup
            next();
        } catch (err) {
            next(err); // ensure errors are passed to Mongoose properly
        }
    } else {
        next();
    }
});

// Store original values before any modifications for audit logging
vendorSchema.pre('save', async function(next) {
  if (!this.isNew && this.isModified()) {
    // Get the original document from database
    try {
      const original = await this.constructor.findById(this._id).lean();
      this.$__originalDoc = original;
    } catch (error) {
      // If we can't get original, continue without detailed change tracking
      console.warn('Could not fetch original document for audit logging:', error.message);
    }
  }
  next();
});

// Pre-save middleware to update modifiedOn and add comprehensive audit log
vendorSchema.pre('save', function(next) {
  const now = new Date();

  if (this.isModified() && !this.isNew) {
    this.modifiedOn = now;

    // Track changes and create audit log entry
    const changes = [];
    const modifiedPaths = this.modifiedPaths();

    // Exclude audit log and system fields from change tracking
    const excludeFields = ['auditLog', 'modifiedOn', 'modifiedBy', '__v'];
    const relevantChanges = modifiedPaths.filter(path => !excludeFields.includes(path));

    if (relevantChanges.length > 0 && this.$__originalDoc) {
      relevantChanges.forEach(path => {
        const originalValue = this.$__originalDoc[path];
        const newValue = this[path];

        // Handle nested objects (like address)
        if (typeof originalValue === 'object' && typeof newValue === 'object' && originalValue !== null && newValue !== null) {
          // For nested objects, compare JSON strings
          if (JSON.stringify(originalValue) !== JSON.stringify(newValue)) {
            changes.push(`${path} updated`);
          }
        } else if (originalValue !== newValue) {
          // Handle primitive values
          const oldVal = originalValue === null || originalValue === undefined ? 'null' : originalValue;
          const newVal = newValue === null || newValue === undefined ? 'null' : newValue;
          changes.push(`${path} changed from "${oldVal}" to "${newVal}"`);
        }
      });

      let remarks;
      if (changes.length === 0) {
        // No actual changes detected
        return next();
      } else if (changes.length === 1) {
        remarks = changes[0];
      } else if (changes.length <= 3) {
        remarks = changes.join(', ');
      } else {
        // If too many changes, just list the field names
        remarks = `Multiple fields updated: ${relevantChanges.join(', ')}`;
      }

      // Add audit log entry
      const auditEntry = {
        status: 'UPDATED',
        createdOn: now,
        createdBy: this.modifiedBy || 'SYSTEM',
        remarks: remarks
      };

      this.auditLog.push(auditEntry);
    } else if (relevantChanges.length > 0) {
      // Fallback when original document is not available
      const auditEntry = {
        status: 'UPDATED',
        createdOn: now,
        createdBy: this.modifiedBy || 'SYSTEM',
        remarks: `Fields updated: ${relevantChanges.join(', ')}`
      };

      this.auditLog.push(auditEntry);
    }
  }

  next();
});

// Indexes for better query performance
vendorSchema.index({ vendorId: 1 });
vendorSchema.index({ vendorType: 1, status: 1 });
vendorSchema.index({ vendorId: 1, status: 1 });
vendorSchema.index({ vendorId: 1, vendorType: 1});
vendorSchema.index({ createdBy: 1, createdOn: 1 });
vendorSchema.index({ vendorId: 1, franchiseId: 1});
vendorSchema.index({ vendorId: 1, franchiseName: 1});


module.exports = mongoose.model('Vendor', vendorSchema);