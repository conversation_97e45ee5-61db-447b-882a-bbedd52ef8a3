const mongoose = require('mongoose');

/**
 * Generate vendorMarginId in order, form VM00000001 to VM99999999, if vendorMarginId is not provided
 */

const generateVendorMarginId = async (Model) => {
    const lastMargin = await Model.findOne({}, { vendorMarginId: 1, _id: 0 })
        .sort({ vendorMarginId: -1 })
        .limit(1);
    if (lastMargin) {
        const lastId = parseInt(lastMargin.vendorMarginId.substring(2));
        const nextId = lastId + 1;
        return `VM${nextId.toString().padStart(8, '0')}`;
    } else {
        return `VM00000001`;
    }
};

/**
 * VendorMargin Schema for Managing Vendor Profit Margins
 * Implements comprehensive margin management with audit trail
 */
const vendorMarginSchema = new mongoose.Schema({
  vendorMarginId: {
    type: String,
    unique: true,
    index: true
  },
  vendorId: {
    type: String,
    required: true,
    trim: true,
    ref: 'Vendor',
    index: true,
  },
  type: {
    type: String,
    required: true,
    enum: ['Brand', 'Category', 'Product'],
    index: true
  },
  brandId: {
    type: String,
    ref: 'Brand',
    index: true,
    validate: {
      validator: function(value) {
        // brandId is required when type is 'Brand'
        return this.type !== 'Brand' || (value && value.trim().length > 0);
      },
      message: 'Brand ID is required when type is Brand'
    }
  },
  categoryId: {
    type: String,
    ref: 'Category',
    index: true,
    validate: {
      validator: function(value) {
        // categoryId is required when type is 'Category'
        return this.type !== 'Category' || (value && value.trim().length > 0);
      },
      message: 'Category ID is required when type is Category'
    }
  },
  productId: {
    type: String,
    ref: 'Product',
    index: true,
    validate: {
      validator: function(value) {
        // productId is required when type is 'Product'
        return this.type !== 'Product' || (value && value.trim().length > 0);
      },
      message: 'Product ID is required when type is Product'
    }
  },
  margin: {
    type: Number,
    required: true,
    min: [0, 'Margin cannot be negative'],
    max: [100, 'Margin cannot exceed 100%'],
    validate: {
      validator: function(value) {
        return Number.isFinite(value) && value >= 0 && value <= 100;
      },
      message: 'Margin must be a valid number between 0 and 100'
    }
  },
  startDate: {
    type: Date,
    required: true,
    index: true
  },
  endDate: {
    type: Date,
    index: true
  },
  sourceAllBrands: {
    type: Boolean,
    required: true,
    default: false
  },
  isActive: {
    type: Boolean,
    required: true,
    default: true,
    index: true
  },
  createdBy: {
    type: String,
    ref: 'User',
    index: true
  },
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  modifiedBy: {
    type: String,
    index: true
  },
  lastUpdated: {
    type: Date,
    default: Date.now
  },
  deleted: {
    type: Boolean,
    default: false
  },
  auditLog: [
    {
      action: {
        type: String,
        required: true
      },
      createdBy: {
        type: String,
        ref: 'User',
        required: true
      },
      createdAt: {
        type: Date,
        required: true,
        default: Date.now
      },
      remarks: {
        type: String,
        trim: true
      },
    }
    ]
});

//add prehook for id generation

vendorMarginSchema.pre('save', async function(next) {
    if (!this.vendorMarginId) {
        try {
            this.vendorMarginId = await generateVendorMarginId(this.constructor); // avoids circular model lookup
            next();
        } catch (err) {
            next(err); // ensure errors are passed to Mongoose properly
        }
    } else {
        next();
    }
});

// Pre-save middleware to validate duplicate brandId/categoryId for same vendor
vendorMarginSchema.pre('save', async function(next) {
    try {
        // Only check for duplicates if this is a new document or if relevant fields are modified
        if (this.isNew || this.isModified('vendorId') || this.isModified('brandId') || this.isModified('categoryId') || this.isModified('productId') || this.isModified('type')) {
            const query = {
                vendorId: this.vendorId,
                isActive: true,
                _id: { $ne: this._id } // Exclude current document for updates
            };

            // Check for duplicate brandId if type is Brand
            if (this.type === 'Brand' && this.brandId) {
                query.type = 'Brand';
                query.brandId = this.brandId;

                const existingBrandMargin = await this.constructor.findOne(query);
                if (existingBrandMargin) {
                    const error = new Error(`Vendor ${this.vendorId} already has an active margin for brand ${this.brandId}`);
                    error.name = 'ValidationError';
                    return next(error);
                }
            }

            // Check for duplicate categoryId if type is Category
            if (this.type === 'Category' && this.categoryId) {
                query.type = 'Category';
                query.categoryId = this.categoryId;
                delete query.brandId; // Remove brandId from query for category check

                const existingCategoryMargin = await this.constructor.findOne(query);
                if (existingCategoryMargin) {
                    const error = new Error(`Vendor ${this.vendorId} already has an active margin for category ${this.categoryId}`);
                    error.name = 'ValidationError';
                    return next(error);
                }
            }

            // Check for duplicate productId if type is Product
            if (this.type === 'Product' && this.productId) {
                query.type = 'Product';
                query.productId = this.productId;
                delete query.brandId; // Remove brandId from query for product check
                delete query.categoryId; // Remove categoryId from query for product check

                const existingProductMargin = await this.constructor.findOne(query);
                if (existingProductMargin) {
                    const error = new Error(`Vendor ${this.vendorId} already has an active margin for product ${this.productId}`);
                    error.name = 'ValidationError';
                    return next(error);
                }
            }
        }
        next();
    } catch (error) {
        next(error);
    }
});

// Indexes for better query performance
vendorMarginSchema.index({ vendorId: 1, type: 1, isActive: 1 });
vendorMarginSchema.index({ vendorId: 1, brandId: 1, isActive: 1 });
vendorMarginSchema.index({ vendorId: 1, categoryId: 1, isActive: 1 });
vendorMarginSchema.index({ createdBy: 1, createdAt: 1 });

// Compound indexes for complex queries
vendorMarginSchema.index({ vendorId: 1, brandId: 1, categoryId: 1, isActive: 1 });

// Unique compound indexes to prevent duplicate brand/category margins per vendor
// Prevent duplicate active brand margins for same vendor
vendorMarginSchema.index(
  {
    vendorId: 1,
    brandId: 1,
    isActive: 1
  },
  {
    unique: true,
    partialFilterExpression: {
      isActive: true,
      type: 'Brand',
      brandId: { $exists: true, $ne: null }
    },
    name: 'unique_vendor_brand_margin'
  }
);

// Prevent duplicate active category margins for same vendor
vendorMarginSchema.index(
  {
    vendorId: 1,
    categoryId: 1,
    isActive: 1
  },
  {
    unique: true,
    partialFilterExpression: {
      isActive: true,
      type: 'Category',
      categoryId: { $exists: true, $ne: null }
    },
    name: 'unique_vendor_category_margin'
  }
);

// General compound index for query performance
vendorMarginSchema.index(
  {
    vendorId: 1,
    type: 1,
    brandId: 1,
    categoryId: 1,
    isActive: 1
  },
  {
    name: 'vendor_margin_compound_index'
  }
);

// Store original values before any modifications
vendorMarginSchema.pre('save', async function(next) {
  if (!this.isNew && this.isModified()) {
    // Get the original document from database
    try {
      const original = await this.constructor.findById(this._id).lean();
      this.$__originalDoc = original;
    } catch (error) {
      // If we can't get original, continue without detailed change tracking
      console.warn('Could not fetch original document for audit logging:', error.message);
    }
  }
  next();
});

// Pre-save middleware to update modifiedOn and add comprehensive audit log
vendorMarginSchema.pre('save', function(next) {
  const now = new Date();

  if (this.isModified() && !this.isNew) {
    this.lastUpdated = now;

    // Track changes and create audit log entry
    const changes = [];
    const modifiedPaths = this.modifiedPaths();

    // Exclude audit log and system fields from change tracking
    const excludeFields = ['auditLog', 'lastUpdated', 'modifiedBy', '__v'];
    const relevantChanges = modifiedPaths.filter(path => !excludeFields.includes(path));

    if (relevantChanges.length > 0 && this.$__originalDoc) {
      relevantChanges.forEach(path => {
        const originalValue = this.$__originalDoc[path];
        const newValue = this[path];

        if (originalValue !== newValue) {
          // Handle different data types appropriately
          const oldVal = originalValue === null || originalValue === undefined ? 'null' : originalValue;
          const newVal = newValue === null || newValue === undefined ? 'null' : newValue;
          changes.push(`${path} changed from "${oldVal}" to "${newVal}"`);
        }
      });

      let remarks;
      if (changes.length === 0) {
        // No actual changes detected
        return next();
      } else if (changes.length === 1) {
        remarks = changes[0];
      } else if (changes.length <= 3) {
        remarks = changes.join(', ');
      } else {
        // If too many changes, just list the field names
        remarks = `Multiple fields updated: ${relevantChanges.join(', ')}`;
      }

      // Add audit log entry
      const auditEntry = {
        action: 'DOCUMENT_UPDATED',
        modifiedBy: this.modifiedBy || 'SYSTEM',
        modifiedOn: now,
        remarks: remarks
      };

      this.auditLog.push(auditEntry);
    } else if (relevantChanges.length > 0) {
      // Fallback when original document is not available
      const auditEntry = {
        action: 'DOCUMENT_UPDATED',
        modifiedBy: this.modifiedBy || 'SYSTEM',
        modifiedOn: now,
        remarks: `Fields updated: ${relevantChanges.join(', ')}`
      };

      this.auditLog.push(auditEntry);
    }
  }

  next();
});


module.exports = mongoose.model('VendorMargin', vendorMarginSchema);