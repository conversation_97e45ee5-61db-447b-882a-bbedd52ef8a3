const express = require('express');
const router = express.Router();
const vendorController = require('../controller/vendor.controller');
const vendorMarginController = require('../controller/vendorMargin.controller');
const { validateVendorMarginCreationMiddleware, validateVendorMarginUpdateMiddleware } = require('../validation/vendorMarginValidation');
const { validateVendorCreationMiddleware, validateVendorUpdateMiddleware } = require('../validation/vendorValidation');

/**
 * Vendor Routes
 * Handles routing for vendor and vendor margin management operations
 */

// ===== VENDOR MARGIN MANAGEMENT ROUTES =====
// NOTE: These routes must come BEFORE the generic /:id route to avoid conflicts

/**
 * @route   POST /vendor/margin
 * @desc    Create new vendor margin
 * @access  Private
 */
router.post('/margin',
  validateVendorMarginCreationMiddleware,
  vendorMarginController.createVendorMargin
);

/**
 * @route   GET /vendor/margin
 * @desc    Get all vendor margins with pagination and filtering
 * @access  Private
 */
router.get('/margin', vendorMarginController.getVendorMargins);

/**
 * @route   GET /vendor/margin/:id
 * @desc    Get vendor margin by ID (supports both MongoDB _id and custom vendorMarginId)
 * @access  Private
 */
router.get('/margin/:id', vendorMarginController.getVendorMarginById);

/**
 * @route   PUT /vendor/margin/:id
 * @desc    Update vendor margin (supports both ID types)
 * @access  Private
 */
router.put('/margin/:id',
  validateVendorMarginUpdateMiddleware,
  vendorMarginController.updateVendorMargin
);

/**
 * @route   DELETE /vendor/margin/:id
 * @desc    Delete vendor margin (supports both ID types)
 * @access  Private
 */
router.delete('/margin/:id', vendorMarginController.deleteVendorMargin);

// ===== ADVANCED VENDOR MARGIN MANAGEMENT ROUTES =====

/**
 * @route   GET /vendor/margin/vendor/:vendorId
 * @desc    Get all margins for a specific vendor
 * @access  Private
 */
router.get('/margin/vendor/:vendorId', vendorMarginController.getMarginsByVendorId);

/**
 * @route   GET /vendor/margin/vendor/:vendorId/brand/:brandId
 * @desc    Get margin for a specific brand by vendor
 * @access  Private
 */
router.get('/margin/vendor/:vendorId/brand/:brandId', vendorMarginController.getBrandMarginByVendor);

/**
 * @route   GET /vendor/margin/vendor/:vendorId/category/:categoryId
 * @desc    Get margin for a specific category by vendor
 * @access  Private
 */
router.get('/margin/vendor/:vendorId/category/:categoryId', vendorMarginController.getCategoryMarginByVendor);

/**
 * @route   PUT /vendor/margin/vendor/:vendorId/margin/:marginId/percentage
 * @desc    Update margin percentage
 * @access  Private
 */
router.put('/margin/vendor/:vendorId/margin/:marginId/percentage', vendorMarginController.updateMarginPercentage);

/**
 * @route   PUT /vendor/margin/vendor/:vendorId/margin/:marginId/brand
 * @desc    Update brandId for a brand margin
 * @access  Private
 */
router.put('/margin/vendor/:vendorId/margin/:marginId/brand', vendorMarginController.updateBrandMargin);

/**
 * @route   PUT /vendor/margin/vendor/:vendorId/margin/:marginId/category
 * @desc    Update categoryId for a category margin
 * @access  Private
 */
router.put('/margin/vendor/:vendorId/margin/:marginId/category', vendorMarginController.updateCategoryMargin);

/**
 * @route   POST /vendor/margin/vendor/:vendorId/brand
 * @desc    Add new brand margin for a vendor
 * @access  Private
 */
router.post('/margin/vendor/:vendorId/brand', vendorMarginController.addBrandMargin);

/**
 * @route   POST /vendor/margin/vendor/:vendorId/category
 * @desc    Add new category margin for a vendor
 * @access  Private
 */
router.post('/margin/vendor/:vendorId/category', vendorMarginController.addCategoryMargin);

/**
 * @route   GET /vendor/margin/vendor/:vendorId/brand/:brandId/category/:categoryId
 * @desc    Get margin for a specific brand-category combination by vendor
 * @access  Private
 */
router.get('/margin/vendor/:vendorId/brand/:brandId/category/:categoryId', vendorMarginController.getBrandCategoryMarginByVendor);

/**
 * @route   POST /vendor/margin/vendor/:vendorId/brand-category
 * @desc    Add new brand-category margin for a vendor
 * @access  Private
 */
router.post('/margin/vendor/:vendorId/brand-category', vendorMarginController.addBrandCategoryMargin);

// ===== VENDOR MANAGEMENT ROUTES =====
// NOTE: These routes come AFTER margin routes to avoid route conflicts

/**
 * @route   GET /vendor/status/:status
 * @desc    Get vendors by status (must come before /:id)
 * @access  Private
 */
router.get('/status/:status', vendorController.getVendorsByStatus);

/**
 * @route   POST /vendor/
 * @desc    Create new vendor
 * @access  Private
 */
router.post('/',
  validateVendorCreationMiddleware,
  vendorController.createVendor
);

/**
 * @route   GET /vendor/
 * @desc    Get all vendors with pagination and filtering
 * @access  Private
 */
router.get('/', vendorController.getVendors);

/**
 * @route   GET /vendor/:id
 * @desc    Get vendor by ID (supports both MongoDB _id and custom vendorId)
 * @access  Private
 */
router.get('/:id', vendorController.getVendorById);

/**
 * @route   PUT /vendor/:id
 * @desc    Update vendor by ID (supports both ID types)
 * @access  Private
 */
router.put('/:id',
  validateVendorUpdateMiddleware,
  vendorController.updateVendor
);

/**
 * @route   PATCH /vendor/:id/status
 * @desc    Update vendor status (supports both ID types)
 * @access  Private
 */
router.patch('/:id/status', vendorController.updateVendorStatus);

/**
 * @route   DELETE /vendor/:id
 * @desc    Delete vendor (supports both ID types)
 * @access  Private
 */
router.delete('/:id', vendorController.deleteVendor);

module.exports = router;
