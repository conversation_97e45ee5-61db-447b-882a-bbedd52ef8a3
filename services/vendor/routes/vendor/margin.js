const express = require('express');
const router = express.Router();

/**
 * Vendor Margin Routes - MUST come before /:id route
 * Implements RESTful API endpoints for vendor margin management
 */

// GET /vendor/margin - List all vendor margins with pagination and filtering
router.get('/margin', vendorMarginController.getVendorMargins);

// GET /vendor/margin/:id - Get vendor margin by ID
router.get('/margin/:id', vendorMarginController.getVendorMarginById);

// POST /vendor/margin - Create new vendor margin
router.post('/margin',
  validateVendorMarginCreationMiddleware,
  vendorMarginController.createVendorMargin
);

// PUT /vendor/margin/:id - Update vendor margin
router.put('/margin/:id',
  validateVendorMarginUpdateMiddleware,
  vendorMarginController.updateVendorMargin
);

// DELETE /vendor/margin/:id - Delete vendor margin
router.delete('/margin/:id', vendorMarginController.deleteVendorMargin);

// Advanced Vendor Margin Management Routes

// GET /vendor/margin/vendor/:vendorId - Get all margins for a specific vendor
router.get('/margin/vendor/:vendorId', vendorMarginController.getMarginsByVendorId);

// GET /vendor/margin/vendor/:vendorId/brand/:brandId - Get margin for a specific brand by vendor
router.get('/margin/vendor/:vendorId/brand/:brandId', vendorMarginController.getBrandMarginByVendor);

// GET /vendor/margin/vendor/:vendorId/category/:categoryId - Get margin for a specific category by vendor
router.get('/margin/vendor/:vendorId/category/:categoryId', vendorMarginController.getCategoryMarginByVendor);

// PUT /vendor/margin/vendor/:vendorId/margin/:marginId/percentage - Update margin percentage
router.put('/margin/vendor/:vendorId/margin/:marginId/percentage', vendorMarginController.updateMarginPercentage);

// PUT /vendor/margin/vendor/:vendorId/margin/:marginId/brand - Update brandId for a brand margin
router.put('/margin/vendor/:vendorId/margin/:marginId/brand', vendorMarginController.updateBrandMargin);

// PUT /vendor/margin/vendor/:vendorId/margin/:marginId/category - Update categoryId for a category margin
router.put('/margin/vendor/:vendorId/margin/:marginId/category', vendorMarginController.updateCategoryMargin);

// POST /vendor/margin/vendor/:vendorId/brand - Add new brand margin for a vendor
router.post('/margin/vendor/:vendorId/brand', vendorMarginController.addBrandMargin);

// POST /vendor/margin/vendor/:vendorId/category - Add new category margin for a vendor
router.post('/margin/vendor/:vendorId/category', vendorMarginController.addCategoryMargin);

// Basic Vendor Management Routes

// GET /vendor/ - List all vendors with pagination and filtering
router.get('/', vendorController.getVendors);

// GET /vendor/:id - Get vendor by ID
router.get('/:id', vendorController.getVendorById);

// POST /vendor/ - Create new vendor
router.post('/',
  validateVendorCreationMiddleware,
  vendorController.createVendor
);

// PUT /vendor/:id - Update vendor
router.put('/:id',
  validateVendorUpdateMiddleware,
  vendorController.updateVendor
);

// PATCH /vendor/:id/status - Update vendor status
router.patch('/:id/status', vendorController.updateVendorStatus);

// DELETE /vendor/:id - Delete vendor
router.delete('/:id', vendorController.deleteVendor);

module.exports = router;