const express = require('express');
const router = express.Router();
const vendorController = require('../../controller/vendor.controller');
const vendorMarginController = require('../../controller/vendorMargin.controller');
const { validateVendorMarginCreationMiddleware, validateVendorMarginUpdateMiddleware } = require('../../validation/vendorMarginValidation');

const { validateVendorCreationMiddleware, validateVendorUpdateMiddleware } = require('../../validation/vendorValidation');

/**
 * Vendor Routes
 * Implements RESTful API endpoints for vendor management
 */

// GET /vendor/status/:status - Get vendors by status (must come before /:id)
router.get('/status/:status', vendorController.getVendorsByStatus);

module.exports = router;