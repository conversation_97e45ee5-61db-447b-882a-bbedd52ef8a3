/**
 * Error Handling Middleware (Node.js v6 compatible)
 * Centralized error handling for the vendor service
 */

/**
 * Custom Error Classes
 */
function AppError(message, statusCode, isOperational) {
  Error.captureStackTrace(this, this.constructor);
  this.message = message;
  this.statusCode = statusCode;
  this.isOperational = isOperational !== undefined ? isOperational : true;
  this.timestamp = new Date().toISOString();
}
AppError.prototype = Object.create(Error.prototype);
AppError.prototype.constructor = AppError;

function ValidationError(message, details) {
  AppError.call(this, message, 400);
  this.details = details || [];
  this.type = 'ValidationError';
}
ValidationError.prototype = Object.create(AppError.prototype);
ValidationError.prototype.constructor = ValidationError;

function AuthenticationError(message) {
  AppError.call(this, message || 'Authentication failed', 401);
  this.type = 'AuthenticationError';
}
AuthenticationError.prototype = Object.create(AppError.prototype);
AuthenticationError.prototype.constructor = AuthenticationError;

function AuthorizationError(message) {
  AppError.call(this, message || 'Access denied', 403);
  this.type = 'AuthorizationError';
}
AuthorizationError.prototype = Object.create(AppError.prototype);
AuthorizationError.prototype.constructor = AuthorizationError;

function NotFoundError(message) {
  AppError.call(this, message || 'Resource not found', 404);
  this.type = 'NotFoundError';
}
NotFoundError.prototype = Object.create(AppError.prototype);
NotFoundError.prototype.constructor = NotFoundError;

function ConflictError(message) {
  AppError.call(this, message || 'Resource conflict', 409);
  this.type = 'ConflictError';
}
ConflictError.prototype = Object.create(AppError.prototype);
ConflictError.prototype.constructor = ConflictError;

function RateLimitError(message) {
  AppError.call(this, message || 'Too many requests', 429);
  this.type = 'RateLimitError';
}
RateLimitError.prototype = Object.create(AppError.prototype);
RateLimitError.prototype.constructor = RateLimitError;

function DatabaseError(message) {
  AppError.call(this, message || 'Database operation failed', 500);
  this.type = 'DatabaseError';
}
DatabaseError.prototype = Object.create(AppError.prototype);
DatabaseError.prototype.constructor = DatabaseError;

/**
 * Error Handler Factory
 */
var ErrorHandler = {
  badRequest: function(message, details) {
    return new ValidationError(message, details);
  },
  unauthorized: function(message) {
    return new AuthenticationError(message);
  },
  forbidden: function(message) {
    return new AuthorizationError(message);
  },
  notFound: function(message) {
    return new NotFoundError(message);
  },
  conflict: function(message) {
    return new ConflictError(message);
  },
  tooManyRequests: function(message) {
    return new RateLimitError(message);
  },
  internal: function(message) {
    return new AppError(message, 500);
  },
  database: function(message) {
    return new DatabaseError(message);
  }
};

/**
 * Handle Mongoose validation errors
 */
const handleMongooseValidationError = function(error) {
  let errors = [];

  if (error.errors && typeof error.errors === 'object') {
    errors = Object.keys(error.errors).map(function(key) {
      const err = error.errors[key];
      return {
        field: err.path || key,
        message: err.message || 'Validation error',
        value: err.value
      };
    });
  } else {
    // Fallback for cases where error.errors is not available
    errors = [{
      field: 'unknown',
      message: error.message || 'Validation failed',
      value: null
    }];
  }

  return new ValidationError('Validation failed', errors);
};

/**
 * Handle Mongoose duplicate key errors
 */
const handleMongoDuplicateKeyError = function(error) {
  let field = 'field';
  let value = 'value';

  if (error.keyValue && typeof error.keyValue === 'object') {
    const keys = Object.keys(error.keyValue);
    if (keys.length > 0) {
      field = keys[0];
      value = error.keyValue[field];
    }
  }

  return new ConflictError(field + " '" + value + "' already exists");
};

/**
 * Handle Mongoose cast errors
 */
const handleMongoCastError = function(error) {
  return new ValidationError('Invalid ' + error.path + ': ' + error.value);
};

/**
 * Handle JWT errors
 */
const handleJWTError = function(error) {
  if (error.name === 'JsonWebTokenError') {
    return new AuthenticationError('Invalid token');
  }
  if (error.name === 'TokenExpiredError') {
    return new AuthenticationError('Token expired');
  }
  return new AuthenticationError('Authentication failed');
};

/**
 * Send error response in development
 */
const sendErrorDev = function(err, req, res) {
  res.status(err.statusCode || 500).json({
    success: false,
    error: err.type || 'Error',
    message: err.message,
    details: err.details || undefined,
    stack: err.stack,
    requestId: req.requestId,
    timestamp: err.timestamp || new Date().toISOString()
  });
};

/**
 * Send error response in production
 */
const sendErrorProd = function(err, req, res) {
  // Operational errors: send message to client
  if (err.isOperational) {
    res.status(err.statusCode).json({
      success: false,
      error: err.type || 'Error',
      message: err.message,
      details: err.details || undefined,
      requestId: req.requestId,
      timestamp: err.timestamp || new Date().toISOString()
    });
  } else {
    // Programming errors: don't leak error details
    console.error('ERROR:', err);

    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Something went wrong',
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Global error handling middleware
 */
const globalErrorHandler = function(err, req, res, next) {
  var error = Object.assign({}, err);
  error.message = err.message;

  // Log error
  console.error('[' + req.requestId + '] Error:', {
    name: err.name,
    message: err.message,
    stack: err.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip
  });

  // Mongoose validation error
  if (err.name === 'ValidationError') {
    // Check if it's a proper Mongoose validation error or a custom validation error
    if (err.errors && typeof err.errors === 'object') {
      error = handleMongooseValidationError(err);
    } else {
      // Handle custom validation errors (like duplicate brand/category)
      error = new ValidationError(err.message || 'Validation failed', []);
      error.statusCode = 400; // Ensure proper status code
    }
  }

  // Mongoose duplicate key error
  if (err.code === 11000) {
    error = handleMongoDuplicateKeyError(err);
  }

  // Mongoose cast error
  if (err.name === 'CastError') {
    error = handleMongoCastError(err);
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError' || err.name === 'TokenExpiredError') {
    error = handleJWTError(err);
  }

  // Send error response
  if (process.env.NODE_ENV === 'development') {
    sendErrorDev(error, req, res);
  } else {
    sendErrorProd(error, req, res);
  }
};

/**
 * Handle unhandled routes
 */
const notFoundHandler = function(req, res, next) {
  const error = new NotFoundError('Cannot ' + req.method + ' ' + req.originalUrl);
  next(error);
};

/**
 * Async error wrapper
 */
const asyncHandler = function(fn) {
  return function(req, res, next) {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

module.exports = {
  AppError: AppError,
  ValidationError: ValidationError,
  AuthenticationError: AuthenticationError,
  AuthorizationError: AuthorizationError,
  NotFoundError: NotFoundError,
  ConflictError: ConflictError,
  RateLimitError: RateLimitError,
  DatabaseError: DatabaseError,
  ErrorHandler: ErrorHandler,
  globalErrorHandler: globalErrorHandler,
  notFoundHandler: notFoundHandler,
  asyncHandler: asyncHandler
};