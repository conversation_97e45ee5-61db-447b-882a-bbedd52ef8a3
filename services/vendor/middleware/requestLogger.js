const { v4: uuidv4 } = require('uuid');

/**
 * Request Logging Middleware (Node.js v6 compatible)
 * Provides comprehensive request/response logging and tracking
 */

/**
 * Generate unique request ID
 */
const generateRequestId = (req, res, next) => {
  req.requestId = uuidv4();
  res.setHeader('X-Request-ID', req.requestId);
  next();
};

/**
 * Request logger middleware
 */
const requestLogger = (req, res, next) => {
  const startTime = Date.now();
  const timestamp = new Date().toISOString();

  // Log request details
  console.log(`[${req.requestId}] ${timestamp} ${req.method} ${req.originalUrl} - ${req.ip}`);

  // Log request body for POST/PUT requests (excluding sensitive data)
  if (['POST', 'PUT', 'PATCH'].includes(req.method) && req.body) {
    const logBody = Object.assign({}, req.body);

    // Remove sensitive fields from logs
    const sensitiveFields = ['password', 'token', 'refreshToken', 'currentPassword', 'newPassword'];
    sensitiveFields.forEach(function(field) {
      if (logBody[field]) {
        logBody[field] = '[REDACTED]';
      }
    });

    console.log(`[${req.requestId}] Request Body:`);
  }

  // Log query parameters
  if (Object.keys(req.query).length > 0) {
    console.log(`[${req.requestId}] Query Params:`, req.query);
  }

  // Override res.json to log response
  const originalJson = res.json;
  res.json = function(data) {
    const endTime = Date.now();
    const duration = endTime - startTime;

    // Log response data (including tokens for debugging)
    if (data && typeof data === 'object') {
      console.log(`[${req.requestId}] Response:`);
    }

    return originalJson.call(this, data);
  };

  next();
};

/**
 * Error logger middleware
 */
const errorLogger = (err, req, res, next) => {
  const timestamp = new Date().toISOString();

  console.error(`[${req.requestId}] ${timestamp} ERROR:`, {
    message: err.message,
    stack: err.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  next(err);
};

/**
 * Performance monitoring middleware (Node.js v6 compatible)
 */
const performanceMonitor = (req, res, next) => {
  const startTime = Date.now();

  res.on('finish', function() {
    const endTime = Date.now();
    const duration = endTime - startTime;

    // Log slow requests (> 1 second)
    if (duration > 1000) {
      console.warn(`[${req.requestId}] SLOW REQUEST: ${req.method} ${req.originalUrl} - ${duration}ms`);
    }

    // Log performance metrics
    console.log(`[${req.requestId}] Performance: ${duration}ms`);
  });

  next();
};

/**
 * Security headers middleware
 */
const securityHeaders = (req, res, next) => {
  // Set security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');

  // Remove server information
  res.removeHeader('X-Powered-By');

  next();
};

/**
 * Request timeout middleware
 */
const requestTimeout = function(timeout) {
  timeout = timeout || 30000;
  return function(req, res, next) {
    const timer = setTimeout(function() {
      if (!res.headersSent) {
        res.status(408).json({
          success: false,
          error: 'Request timeout',
          message: 'Request took too long to process',
          requestId: req.requestId,
          timestamp: new Date().toISOString()
        });
      }
    }, timeout);

    res.on('finish', function() {
      clearTimeout(timer);
    });

    next();
  };
};

module.exports = {
  generateRequestId: generateRequestId,
  requestLogger: requestLogger,
  errorLogger: errorLogger,
  performanceMonitor: performanceMonitor,
  securityHeaders: securityHeaders,
  requestTimeout: requestTimeout
};