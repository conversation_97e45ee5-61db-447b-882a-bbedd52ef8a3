const jwt = require('jsonwebtoken');

// Use the same secret as in the auth middleware
const jwtSecret = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';

// Create a test token
const testPayload = {
  userId: 'test-user-123',
  email: '<EMAIL>',
  name: 'Test User',
  type: 'Admin'
};

const token = jwt.sign(testPayload, jwtSecret, { expiresIn: '24h' });

console.log('Test JWT Token:');
console.log(token);
console.log('\nUse this token in Authorization header as:');
console.log(`Authorization: Bearer ${token}`);
