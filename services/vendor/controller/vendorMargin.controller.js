const vendorMarginService = require('../services/vendorMarginService');
const { asyncHandler } = require('../middleware/errorHandler');

/**
 * VendorMargin Controller
 * Handles HTTP requests for vendor margin management operations
 */

/**
 * Create a new vendor margin
 * POST /vendor/margin
 */
const createVendorMargin = asyncHandler(async (req, res, next) => {
  const marginData = req.validatedData || req.body;

  // Add creator information if available from auth middleware
  if (req.user && req.user.userId) {
    marginData.createdBy = req.user.userId;
  } else if (!marginData.createdBy) {
    // Provide default value when no auth middleware is present
    marginData.createdBy = 'SYSTEM';
  }

  // Extract user token for external service calls
  let userToken = null;
  if (req.user && req.token) {
    userToken = req.token;
  } else if (req.headers.authorization) {
    // Extract token directly from Authorization header if auth middleware not applied
    const authHeader = req.headers.authorization;
    if (authHeader.startsWith('Bearer ')) {
      userToken = authHeader.substring(7);
    } else if (authHeader.startsWith('JWT ')) {
      userToken = authHeader.substring(4);
    }
  }

  const result = await vendorMarginService.createVendorMargin(marginData, userToken);

  res.status(201).json({
    success: true,
    message: result.message,
    data: result.margin,
    requestId: req.requestId,
    timestamp: new Date().toISOString()
  });
});

/**
 * Get vendor margin by ID
 * GET /vendors/:id
 */
const getVendorMarginById = asyncHandler(async (req, res, next) => {
  const { id } = req.params;
  const result = await vendorMarginService.getVendorMarginById(id);

  res.status(200).json({
    success: true,
    message: result.message,
    data: result.margin,
    requestId: req.requestId,
    timestamp: new Date().toISOString()
  });
});

/**
 * Get all vendor margins with pagination and filtering
 * GET /vendors
 */
const getVendorMargins = asyncHandler(async (req, res, next) => {
  const { page = 1, limit = 10, vendorId, type, brandId, categoryId } = req.query;

  const filters = {};
  if (vendorId) filters.vendorId = vendorId;
  if (type) filters.type = type;
  if (brandId) filters.brandId = brandId;
  if (categoryId) filters.categoryId = categoryId;

  const result = await vendorMarginService.getVendorMargins(page, limit, filters);

  res.status(200).json({
    success: true,
    message: 'Vendor margins retrieved successfully',
    data: result.margins,
    pagination: result.pagination,
    requestId: req.requestId,
    timestamp: new Date().toISOString()
  });
});

const updateVendorMargin = asyncHandler(async (req, res, next) => {
  const { id } = req.params;
  const updateData = req.validatedData || req.body;
  const modifiedBy = req.user?.userId || 'SYSTEM';

  const result = await vendorMarginService.updateVendorMargin(id, updateData, modifiedBy);

  res.status(200).json({
    success: true,
    message: result.message,
    requestId: req.requestId,
    timestamp: new Date().toISOString()
  });
});

const deleteVendorMargin = asyncHandler(async (req, res, next) => {
  const { id } = req.params;
  const result = await vendorMarginService.deleteVendorMargin(id);

  res.status(200).json({
    success: true,
    message: result.message,
    requestId: req.requestId,
    timestamp: new Date().toISOString()
  });
});

/**
 * Get all margins for a specific vendor
 * GET /vendor/margin/vendor/:vendorId
 */
const getMarginsByVendorId = asyncHandler(async (req, res, next) => {
  const { vendorId } = req.params;
  const { page = 1, limit = 10 } = req.query;

  const result = await vendorMarginService.getMarginsByVendorId(vendorId, page, limit);

  res.status(200).json({
    success: true,
    message: result.message,
    data: result.margins,
    pagination: result.pagination,
    requestId: req.requestId,
    timestamp: new Date().toISOString()
  });
});

/**
 * Get margin for a specific brand by vendor
 * GET /vendor/margin/vendor/:vendorId/brand/:brandId
 */
const getBrandMarginByVendor = asyncHandler(async (req, res, next) => {
  const { vendorId, brandId } = req.params;

  const result = await vendorMarginService.getBrandMarginByVendor(vendorId, brandId);

  res.status(200).json({
    success: true,
    message: result.message,
    data: result.margin,
    requestId: req.requestId,
    timestamp: new Date().toISOString()
  });
});

/**
 * Get margin for a specific category by vendor
 * GET /vendor/margin/vendor/:vendorId/category/:categoryId
 */
const getCategoryMarginByVendor = asyncHandler(async (req, res, next) => {
  const { vendorId, categoryId } = req.params;

  const result = await vendorMarginService.getCategoryMarginByVendor(vendorId, categoryId);

  res.status(200).json({
    success: true,
    message: result.message,
    data: result.margin,
    requestId: req.requestId,
    timestamp: new Date().toISOString()
  });
});

/**
 * Update margin percentage for a specific vendor margin
 * PUT /vendor/margin/vendor/:vendorId/margin/:marginId/percentage
 */
const updateMarginPercentage = asyncHandler(async (req, res, next) => {
  const { vendorId, marginId } = req.params;
  const { margin } = req.body;
  const modifiedBy = req.user?.userId || 'SYSTEM';

  if (!margin || margin < 0 || margin > 100) {
    return res.status(400).json({
      success: false,
      message: 'Valid margin percentage (0-100) is required',
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  }

  const result = await vendorMarginService.updateMarginPercentage(vendorId, marginId, margin, modifiedBy);

  res.status(200).json({
    success: true,
    message: result.message,
    data: result.margin,
    requestId: req.requestId,
    timestamp: new Date().toISOString()
  });
});

/**
 * Update brandId for a brand margin
 * PUT /vendor/margin/vendor/:vendorId/margin/:marginId/brand
 */
const updateBrandMargin = asyncHandler(async (req, res, next) => {
  const { vendorId, marginId } = req.params;
  const { brandId } = req.body;
  const modifiedBy = req.user?.userId || 'SYSTEM';

  if (!brandId) {
    return res.status(400).json({
      success: false,
      message: 'Brand ID is required',
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  }

  const result = await vendorMarginService.updateBrandMargin(vendorId, marginId, brandId, modifiedBy);

  res.status(200).json({
    success: true,
    message: result.message,
    data: result.margin,
    requestId: req.requestId,
    timestamp: new Date().toISOString()
  });
});

/**
 * Update categoryId for a category margin
 * PUT /vendor/margin/vendor/:vendorId/margin/:marginId/category
 */
const updateCategoryMargin = asyncHandler(async (req, res, next) => {
  const { vendorId, marginId } = req.params;
  const { categoryId } = req.body;
  const modifiedBy = req.user?.userId || 'SYSTEM';

  if (!categoryId) {
    return res.status(400).json({
      success: false,
      message: 'Category ID is required',
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  }

  const result = await vendorMarginService.updateCategoryMargin(vendorId, marginId, categoryId, modifiedBy);

  res.status(200).json({
    success: true,
    message: result.message,
    data: result.margin,
    requestId: req.requestId,
    timestamp: new Date().toISOString()
  });
});

/**
 * Add new brand margin for a vendor
 * POST /vendor/margin/vendor/:vendorId/brand
 */
const addBrandMargin = asyncHandler(async (req, res, next) => {
  const { vendorId } = req.params;
  const { brandId, margin, startDate, endDate } = req.body;
  const createdBy = req.user?.userId || 'SYSTEM';

  if (!brandId || !margin || !startDate) {
    return res.status(400).json({
      success: false,
      message: 'Brand ID, margin percentage, and start date are required',
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  }

  // Extract user token for external service calls
  let userToken = null;
  if (req.user && req.token) {
    userToken = req.token;
  } else if (req.headers.authorization) {
    // Extract token directly from Authorization header if auth middleware not applied
    const authHeader = req.headers.authorization;
    if (authHeader.startsWith('Bearer ')) {
      userToken = authHeader.substring(7);
    } else if (authHeader.startsWith('JWT ')) {
      userToken = authHeader.substring(4);
    }
  }

  const result = await vendorMarginService.addBrandMargin(vendorId, brandId, margin, startDate, endDate, createdBy, userToken);

  res.status(201).json({
    success: true,
    message: result.message,
    data: result.margin,
    requestId: req.requestId,
    timestamp: new Date().toISOString()
  });
});

/**
 * Add new category margin for a vendor
 * POST /vendor/margin/vendor/:vendorId/category
 */
const addCategoryMargin = asyncHandler(async (req, res, next) => {
  const { vendorId } = req.params;
  const { categoryId, margin, startDate, endDate } = req.body;
  const createdBy = req.user?.userId || 'SYSTEM';

  if (!categoryId || !margin || !startDate) {
    return res.status(400).json({
      success: false,
      message: 'Category ID, margin percentage, and start date are required',
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  }

  // Extract user token for external service calls
  let userToken = null;
  if (req.user && req.token) {
    userToken = req.token;
  } else if (req.headers.authorization) {
    // Extract token directly from Authorization header if auth middleware not applied
    const authHeader = req.headers.authorization;
    if (authHeader.startsWith('Bearer ')) {
      userToken = authHeader.substring(7);
    } else if (authHeader.startsWith('JWT ')) {
      userToken = authHeader.substring(4);
    }
  }

  const result = await vendorMarginService.addCategoryMargin(vendorId, categoryId, margin, startDate, endDate, createdBy, userToken);

  res.status(201).json({
    success: true,
    message: result.message,
    data: result.margin,
    requestId: req.requestId,
    timestamp: new Date().toISOString()
  });
});

module.exports = {
  createVendorMargin,
  getVendorMarginById,
  getVendorMargins,
  updateVendorMargin,
  deleteVendorMargin,
  getMarginsByVendorId,
  getBrandMarginByVendor,
  getCategoryMarginByVendor,
  updateMarginPercentage,
  updateBrandMargin,
  updateCategoryMargin,
  addBrandMargin,
  addCategoryMargin
};