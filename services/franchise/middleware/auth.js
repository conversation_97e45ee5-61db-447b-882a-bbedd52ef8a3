const axios = require('axios');

// Configuration
const USER_SERVICE_URL = process.env.USER_SERVICE_URL || 'http://localhost:5001';

/**
 * Authentication Middleware for Franchise Service
 * Verifies JWT tokens by calling the User Service verify-token endpoint
 */

const authenticate = () => {
  return async (req, res, next) => {
    try {
      const authHeader = req.headers.authorization;

      if (!authHeader || !authHeader.startsWith('JWT ')) {
        return res.status(401).json({
          success: false,
          message: 'Invalid Authorization header. Token must start with "JWT "'
        });
      }
      const token = authHeader.substring(4); // Remove 'JWT '
      try {
        const response = await axios.get(`${USER_SERVICE_URL}/auth/verify-token`, {
          headers: {
            'Authorization': `JWT ${token}`,
            'Content-Type': 'application/json'
          },
          timeout: 10000 // 10 second timeout
        });

        if (response.data) {
          req.user = response.data.data.user;
          return next();
        } else {
          return res.status(401).json({
            success: false,
            message: 'Token verification failed'
          });
        }
      } catch (error) {
        console.error(`[${req.requestId}] Token verification error:`, error.message);

        if (error.response) {
          return res.status(error.response.status).json({
            success: false,
            message: error.response.data.message || 'Token verification failed',
            error: error.response.data.error
          });
        } else if (error.code === 'ECONNREFUSED') {
          // User service is not available
          return res.status(503).json({
            success: false,
            message: 'Authentication service unavailable',
            error: 'User service connection failed'
          });
        } else if (error.code === 'ETIMEDOUT') {
          // Request timed out
          return res.status(408).json({
            success: false,
            message: 'Authentication request timed out',
            error: 'Token verification timeout'
          });
        } else {
          // Other network errors
          return res.status(500).json({
            success: false,
            message: 'Authentication service error',
            error: error.message
          });
        }
      }
    } catch (error) {
      console.error(`[${req.requestId}] Authentication middleware error:`, error);
      return res.status(500).json({
        success: false,
        message: 'Internal authentication error',
        error: error.message
      });
    }
  };
};



module.exports = {
  authenticate,
}; 