const { v4: uuidv4 } = require('uuid');

/**
 * Request Logging Middleware
 * Provides comprehensive request/response logging and tracking
 */

/**
 * Generate unique request ID
 */
const generateRequestId = (req, res, next) => {
  req.requestId = uuidv4();
  res.setHeader('X-Request-ID', req.requestId);
  next();
};

/**
 * Request logger middleware
 */
const requestLogger = (req, res, next) => {
  const startTime = Date.now();
  const timestamp = new Date().toISOString();
  
  // Log request details
  console.log(`[${req.requestId}] ${timestamp} ${req.method} ${req.originalUrl} - ${req.ip}`);
  
  // Log request body for POST/PUT requests (excluding sensitive data)
  if (['POST', 'PUT', 'PATCH'].includes(req.method) && req.body) {
    const logBody = { ...req.body };
    
    // Remove sensitive fields from logs
    const sensitiveFields = ['password', 'token', 'refreshToken', 'currentPassword', 'newPassword'];
    sensitiveFields.forEach(field => {
      if (logBody[field]) {
        logBody[field] = '[REDACTED]';
      }
    });
    
    console.log(`[${req.requestId}] Request Body:`, JSON.stringify(logBody, null, 2));
  }

  // Log query parameters
  if (Object.keys(req.query).length > 0) {
    console.log(`[${req.requestId}] Query Params:`, req.query);
  }

  // Override res.json to log response
  const originalJson = res.json;
  res.json = function(data) {
    const endTime = Date.now();
    const duration = endTime - startTime;

    // Log response data (including tokens for debugging)
    if (data && typeof data === 'object') {
      console.log(`[${req.requestId}] Response:`, JSON.stringify(data, null, 2));
    }

    return originalJson.call(this, data);
  };

  next();
};

/**
 * Error logger middleware
 */
const errorLogger = (err, req, res, next) => {
  const timestamp = new Date().toISOString();
  
  console.error(`[${req.requestId}] ${timestamp} ERROR:`, {
    message: err.message,
    stack: err.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });
  
  next(err);
};

/**
 * Performance monitoring middleware
 */
const performanceMonitor = (req, res, next) => {
  const startTime = process.hrtime.bigint();
  
  res.on('finish', () => {
    const endTime = process.hrtime.bigint();
    const duration = Number(endTime - startTime) / 1000000; // Convert to milliseconds
    
    // Log slow requests (> 1 second)
    if (duration > 1000) {
      console.warn(`[${req.requestId}] SLOW REQUEST: ${req.method} ${req.originalUrl} - ${duration.toFixed(2)}ms`);
    }
    
    // Log performance metrics
    console.log(`[${req.requestId}] Performance: ${duration.toFixed(2)}ms`);
  });
  
  next();
};

/**
 * Security headers middleware
 */
const securityHeaders = (req, res, next) => {
  // Set security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // Remove server information
  res.removeHeader('X-Powered-By');
  
  next();
};

/**
 * Request size limiter
 */
const requestSizeLimiter = (limit = '10mb') => {
  return (req, res, next) => {
    const contentLength = parseInt(req.get('Content-Length') || '0');
    const maxSize = parseSize(limit);
    
    if (contentLength > maxSize) {
      return res.status(413).json({
        success: false,
        error: 'Request entity too large',
        message: `Request size exceeds limit of ${limit}`,
        requestId: req.requestId,
        timestamp: new Date().toISOString()
      });
    }
    
    next();
  };
};

/**
 * Parse size string to bytes
 */
const parseSize = (size) => {
  const units = {
    'b': 1,
    'kb': 1024,
    'mb': 1024 * 1024,
    'gb': 1024 * 1024 * 1024
  };
  
  const match = size.toLowerCase().match(/^(\d+(?:\.\d+)?)\s*(b|kb|mb|gb)?$/);
  if (!match) return 0;
  
  const value = parseFloat(match[1]);
  const unit = match[2] || 'b';
  
  return Math.floor(value * units[unit]);
};

/**
 * API versioning middleware
 */
const apiVersioning = (req, res, next) => {
  // Set API version header
  res.setHeader('API-Version', process.env.API_VERSION || '1.0.0');
  
  // Extract version from Accept header or URL
  const acceptHeader = req.get('Accept');
  const versionFromHeader = acceptHeader && acceptHeader.match(/version=(\d+\.\d+)/);
  const versionFromUrl = req.originalUrl.match(/\/v(\d+)/);
  
  req.apiVersion = versionFromHeader ? versionFromHeader[1] : 
                   versionFromUrl ? `${versionFromUrl[1]}.0` : '1.0';
  
  next();
};

/**
 * Request timeout middleware
 */
const requestTimeout = (timeout = 30000) => {
  return (req, res, next) => {
    const timer = setTimeout(() => {
      if (!res.headersSent) {
        res.status(408).json({
          success: false,
          error: 'Request timeout',
          message: 'Request took too long to process',
          requestId: req.requestId,
          timestamp: new Date().toISOString()
        });
      }
    }, timeout);
    
    res.on('finish', () => {
      clearTimeout(timer);
    });
    
    next();
  };
};

module.exports = {
  generateRequestId,
  requestLogger,
  errorLogger,
  performanceMonitor,
  securityHeaders,
  requestSizeLimiter,
  apiVersioning,
  requestTimeout
}; 