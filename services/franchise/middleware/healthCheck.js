const mongoose = require('mongoose');
const { getConnectionStatus } = require('./database');

/**
 * Health Check Middleware
 * Provides comprehensive health monitoring for the franchise service
 */

/**
 * Basic health check endpoint
 */
const healthCheck = (req, res) => {
  const dbStatus = getConnectionStatus();
  const uptime = process.uptime();
  const memoryUsage = process.memoryUsage();
  
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: {
      name: 'franchise-service',
      version: process.env.SERVICE_VERSION || '1.0.0',
      uptime: `${Math.floor(uptime / 60)} minutes`,
      environment: process.env.NODE_ENV || 'development'
    },
    database: {
      status: dbStatus.isConnected ? 'connected' : 'disconnected',
      readyState: dbStatus.readyState,
      host: dbStatus.host,
      name: dbStatus.name
    },
    memory: {
      rss: `${Math.round(memoryUsage.rss / 1024 / 1024)} MB`,
      heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)} MB`,
      heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)} MB`,
      external: `${Math.round(memoryUsage.external / 1024 / 1024)} MB`
    }
  };

  // Check if database is connected
  if (!dbStatus.isConnected) {
    health.status = 'unhealthy';
    return res.status(503).json(health);
  }

  res.status(200).json(health);
};

/**
 * Detailed health check with database connectivity test
 */
const detailedHealthCheck = async (req, res) => {
  const dbStatus = getConnectionStatus();
  const uptime = process.uptime();
  const memoryUsage = process.memoryUsage();
  
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: {
      name: 'franchise-service',
      version: process.env.SERVICE_VERSION || '1.0.0',
      uptime: `${Math.floor(uptime / 60)} minutes`,
      environment: process.env.NODE_ENV || 'development',
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch
    },
    database: {
      status: dbStatus.isConnected ? 'connected' : 'disconnected',
      readyState: dbStatus.readyState,
      host: dbStatus.host,
      name: dbStatus.name
    },
    memory: {
      rss: `${Math.round(memoryUsage.rss / 1024 / 1024)} MB`,
      heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)} MB`,
      heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)} MB`,
      external: `${Math.round(memoryUsage.external / 1024 / 1024)} MB`
    },
    checks: {
      database: 'pending',
      models: 'pending'
    }
  };

  try {
    // Test database connectivity
    if (dbStatus.isConnected) {
      await mongoose.connection.db.admin().ping();
      health.checks.database = 'passed';
      
      // Test model access
      const Franchise = require('../models/Franchise');
      await Franchise.countDocuments().limit(1);
      health.checks.models = 'passed';
    } else {
      health.checks.database = 'failed';
      health.checks.models = 'failed';
      health.status = 'unhealthy';
    }

  } catch (error) {
    health.checks.database = 'failed';
    health.checks.models = 'failed';
    health.status = 'unhealthy';
    health.error = error.message;
  }

  const statusCode = health.status === 'healthy' ? 200 : 503;
  res.status(statusCode).json(health);
};

/**
 * Readiness probe for Kubernetes
 */
const readinessProbe = async (req, res) => {
  const dbStatus = getConnectionStatus();
  
  if (!dbStatus.isConnected) {
    return res.status(503).json({
      status: 'not ready',
      message: 'Database not connected',
      timestamp: new Date().toISOString()
    });
  }

  try {
    // Quick database ping
    await mongoose.connection.db.admin().ping();
    
    res.status(200).json({
      status: 'ready',
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    res.status(503).json({
      status: 'not ready',
      message: 'Database connectivity issue',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Liveness probe for Kubernetes
 */
const livenessProbe = (req, res) => {
  res.status(200).json({
    status: 'alive',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
};

/**
 * Service metrics endpoint
 */
const metrics = async (req, res) => {
  try {
    const Franchise = require('../models/Franchise');
    
    const [
      totalFranchises,
      activeFranchises,
      memoryUsage
    ] = await Promise.all([
      Franchise.countDocuments(),
      Franchise.countDocuments({ 'kycStatus.overallStatus': 'Approved' }),
      Promise.resolve(process.memoryUsage())
    ]);

    const metrics = {
      timestamp: new Date().toISOString(),
      service: {
        uptime: process.uptime(),
        memory: {
          rss: memoryUsage.rss,
          heapTotal: memoryUsage.heapTotal,
          heapUsed: memoryUsage.heapUsed,
          external: memoryUsage.external
        }
      },
      business: {
        totalFranchises,
        activeFranchises,
        inactiveFranchises: totalFranchises - activeFranchises
      }
    };

    res.status(200).json(metrics);
    
  } catch (error) {
    res.status(500).json({
      error: 'Failed to retrieve metrics',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
};

module.exports = {
  healthCheck,
  detailedHealthCheck,
  readinessProbe,
  livenessProbe,
  metrics
}; 