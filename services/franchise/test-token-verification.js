const axios = require('axios');

// Configuration
const FRANCHISE_SERVICE_URL = 'http://localhost:5003';
const USER_SERVICE_URL = 'http://localhost:5001';

// Test token (you'll need to replace this with a valid token from your user service)
const TEST_TOKEN = 'YOUR_VALID_JWT_TOKEN_HERE';

async function testTokenVerification() {
  console.log('🧪 Testing Token Verification in Manpower Service...\n');

  try {
    // Test 1: Call manpower endpoint without token (should fail)
    console.log('1. Testing without token (should fail):');
    try {
      const response = await axios.get(`${FRANCHISE_SERVICE_URL}/manpower`, {
        timeout: 5000
      });
      console.log('❌ Unexpected success without token');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ Correctly rejected request without token');
        console.log('   Response:', error.response.data.message);
      } else {
        console.log('❌ Unexpected error:', error.message);
      }
    }

    console.log('\n2. Testing with invalid token format (should fail):');
    try {
      const response = await axios.get(`${FRANCHISE_SERVICE_URL}/manpower`, {
        headers: {
          'Authorization': 'Bearer invalid-token'
        },
        timeout: 5000
      });
      console.log('❌ Unexpected success with invalid token format');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ Correctly rejected request with invalid token format');
        console.log('   Response:', error.response.data.message);
      } else {
        console.log('❌ Unexpected error:', error.message);
      }
    }

    console.log('\n3. Testing with valid token (should succeed if token is valid):');
    if (TEST_TOKEN === 'YOUR_VALID_JWT_TOKEN_HERE') {
      console.log('⚠️  Please replace TEST_TOKEN with a valid JWT token from your user service');
      console.log('   You can get a token by calling the user service login endpoint');
    } else {
      try {
        const response = await axios.get(`${FRANCHISE_SERVICE_URL}/manpower`, {
          headers: {
            'Authorization': `JWT ${TEST_TOKEN}`
          },
          timeout: 10000
        });
        console.log('✅ Successfully authenticated with valid token');
        console.log('   Response status:', response.status);
        console.log('   User data attached to request');
      } catch (error) {
        if (error.response) {
          console.log('❌ Token verification failed:');
          console.log('   Status:', error.response.status);
          console.log('   Message:', error.response.data.message);
          console.log('   Error:', error.response.data.error);
        } else {
          console.log('❌ Network error:', error.message);
        }
      }
    }

    console.log('\n4. Testing user service verify-token endpoint directly:');
    try {
      const response = await axios.get(`${USER_SERVICE_URL}/auth/verify-token`, {
        headers: {
          'Authorization': `JWT ${TEST_TOKEN}`
        },
        timeout: 5000
      });
      console.log('✅ User service verify-token endpoint is working');
      console.log('   Response:', response.data);
    } catch (error) {
      if (error.response) {
        console.log('❌ User service verify-token failed:');
        console.log('   Status:', error.response.status);
        console.log('   Message:', error.response.data.message);
      } else {
        console.log('❌ User service connection error:', error.message);
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Instructions for getting a valid token
console.log('📋 Instructions for testing:');
console.log('1. Start the user service: cd services/user && npm start');
console.log('2. Start the franchise service: cd services/franchise && npm start');
console.log('3. Get a valid token by calling the user service login endpoint:');
console.log('   POST http://localhost:5001/auth/login');
console.log('   Body: { "mobileNo": "your_mobile", "password": "your_password" }');
console.log('4. Replace TEST_TOKEN in this file with the token from the login response');
console.log('5. Run this test: node test-token-verification.js\n');

// Run the test
testTokenVerification(); 