const franchiseService = require('../services/franchiseService');

/**
 * Get all franchises with pagination and filters
 */
const getAllFranchises = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      type, 
      subType, 
      groupType, 
      city, 
      state, 
      kycStatus, 
      mobile, 
      email 
    } = req.query;

    const filters = {
      type,
      subType,
      groupType,
      city,
      state,
      kycStatus,
      mobile,
      email
    };

    // Remove undefined filters
    Object.keys(filters).forEach(key => {
      if (filters[key] === undefined) {
        delete filters[key];
      }
    });

    await franchiseService.getAllFranchises(
      filters, 
      parseInt(page), 
      parseInt(limit)
    ).then(result => {
      res.status(200).json({
        success: true,
        data: result.franchises,
        pagination: result.pagination
      });
    }).catch(error => {
      res.status(400).json({ "message": error.message || "Failed to get franchises" });
    });

  } catch (err) {
    res.status(500).json({ "message": "something went wrong-getAllFranchises" });
  }
};

/**
 * Get franchise by ID
 */
const getFranchiseById = async (req, res) => {
  try {
    const { franchiseId } = req.params;
    
    if (!franchiseId) {
      return res.status(400).json({ "message": "Franchise ID is required" });
    }

    await franchiseService.getFranchiseById(franchiseId).then(franchise => {
      if (!franchise) {
        res.status(404).json({ "message": "Franchise not found" });
      } else {
        res.status(200).json({
          success: true,
          data: franchise
        });
      }
    }).catch(error => {
      res.status(400).json({ "message": error.message || "Failed to get franchise" });
    });

  } catch (err) {
    res.status(500).json({ "message": "something went wrong-getFranchiseById" });
  }
};

/**
 * Create new franchise
 */
const createFranchise = async (req, res) => {
  try {
    const franchiseData = req.body;
    
    await franchiseService.createFranchise(franchiseData).then(franchise => {
      res.status(201).json({
        success: true,
        message: 'Franchise created successfully',
        data: franchise
      });
    }).catch(error => {
      res.status(400).json({ "message": error.message || "Failed to create franchise" });
    });

  } catch (err) {
    res.status(500).json({ "message": "something went wrong-createFranchise" });
  }
};

module.exports = {
  getAllFranchises,
  getFranchiseById,
  createFranchise
}; 