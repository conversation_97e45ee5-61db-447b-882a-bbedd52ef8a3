const manpowerService = require('../services/manpowerService');

/**
 * Get all manpower with pagination and filters
 */
const getAllManpower = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      type, 
      isDeliveryPerson, 
      isAvailable, 
      status, 
      isActive, 
      name, 
      mobileNo, 
      emailId 
    } = req.query;

    const parentFranchiseId = req.user.franchiseId; // Get from token

    const filters = {
      type,
      isDeliveryPerson,
      isAvailable,
      status,
      isActive,
      name,
      mobileNo,
      emailId
    };

    // Remove undefined filters
    Object.keys(filters).forEach(key => {
      if (filters[key] === undefined) {
        delete filters[key];
      }
    });

    await manpowerService.getAllManpower(
      parentFranchiseId,
      filters, 
      parseInt(page), 
      parseInt(limit)
    ).then(result => {
      res.status(200).json({
        success: true,
        data: result.manpower,
        pagination: result.pagination
      });
    }).catch(error => {
      res.status(400).json({ "message": error.message || "Failed to get manpower" });
    });

  } catch (err) {
    res.status(500).json({ "message": "something went wrong-getAllManpower" });
  }
};

/**
 * Get manpower by ID
 */
const getManpowerById = async (req, res) => {
  try {
    const { manpowerId } = req.params;
    const parentFranchiseId = req.user.franchiseId; // Get from token
    
    if (!manpowerId) {
      return res.status(400).json({ "message": "Manpower ID is required" });
    }

    await manpowerService.getManpowerById(manpowerId, parentFranchiseId).then(manpower => {
      res.status(200).json({
        success: true,
        data: manpower
      });
    }).catch(error => {
      res.status(400).json({ "message": error.message || "Failed to get manpower" });
    });

  } catch (err) {
    res.status(500).json({ "message": "something went wrong-getManpowerById" });
  }
};

/**
 * Create new manpower
 */
const createManpower = async (req, res) => {
  try {
    //console.log("00000000000000000000000",req);
    const manpowerData = req.body;
    const parentFranchiseId = req.user.franchiseId; // Get from token

   // console.log("11111111111111111111111",manpowerData, parentFranchiseId);
    
    await manpowerService.createManpower(manpowerData, parentFranchiseId).then(manpower => {
      res.status(201).json({
        success: true,
        message: 'Manpower created successfully',
        data: manpower
      });
    }).catch(error => {
      res.status(400).json({ "message": error.message || "Failed to create manpower" });
    });

  } catch (err) {
    console.log("22222222222222222222222",err);
    res.status(500).json({ "message": "something went wrong-createManpower" });
  }
};

/**
 * Update manpower
 */
const updateManpower = async (req, res) => {
  try {
    const { manpowerId } = req.params;
    const updateData = req.body;
    const parentFranchiseId = req.user.franchiseId; // Get from token
    
    if (!manpowerId) {
      return res.status(400).json({ "message": "Manpower ID is required" });
    }

    await manpowerService.updateManpower(manpowerId, updateData, parentFranchiseId).then(manpower => {
      res.status(200).json({
        success: true,
        message: 'Manpower updated successfully',
        data: manpower
      });
    }).catch(error => {
      res.status(400).json({ "message": error.message || "Failed to update manpower" });
    });

  } catch (err) {
    res.status(500).json({ "message": "something went wrong-updateManpower" });
  }
};

/**
 * Delete manpower
 */
const deleteManpower = async (req, res) => {
  try {
    const { manpowerId } = req.params;
    const parentFranchiseId = req.user.franchiseId; // Get from token
    
    if (!manpowerId) {
      return res.status(400).json({ "message": "Manpower ID is required" });
    }

    await manpowerService.deleteManpower(manpowerId, parentFranchiseId).then(manpower => {
      res.status(200).json({
        success: true,
        message: 'Manpower deleted successfully',
        data: manpower
      });
    }).catch(error => {
      res.status(400).json({ "message": error.message || "Failed to delete manpower" });
    });

  } catch (err) {
    res.status(500).json({ "message": "something went wrong-deleteManpower" });
  }
};

module.exports = {
  getAllManpower,
  getManpowerById,
  createManpower,
  updateManpower,
  deleteManpower
}; 