const express = require('express');
const router = express.Router();

const franchiseController = require('../controller/franchiseController');

// GET /franchises - Get all franchises with pagination and filters
router.get('/list', franchiseController.getAllFranchises);

// POST /franchises - Create new franchise
router.post('/', franchiseController.createFranchise);

// GET /franchises/:franchiseId - Get franchise by ID
router.get('/:franchiseId', franchiseController.getFranchiseById);

module.exports = router; 