const express = require('express');
const router = express.Router();
const manpowerController = require('../controller/manpowerController');
const auth = require('../middleware/auth');

// Apply authentication middleware to all routes
router.use(auth.authenticate());

// Get all manpower with pagination and filters
router.get('/', manpowerController.getAllManpower);

// Get manpower by ID
router.get('/:manpowerId', manpowerController.getManpowerById);

// Create new manpower
router.post('/', manpowerController.createManpower);

// Update manpower
router.put('/:manpowerId', manpowerController.updateManpower);

// Delete manpower (soft delete)
router.delete('/:manpowerId', manpowerController.deleteManpower);

module.exports = router; 