const Joi = require('joi');

/**
 * Franchise Validation Schemas
 * Validation rules for franchise operations
 */

// Create franchise validation schema
const createFranchiseSchema = Joi.object({
  franchiseId: Joi.string().optional(),
  type: Joi.string().valid('SF', 'RF').required(),
  subType: Joi.string().valid('SFSELLER', 'SFBUYER', 'RFSELLER', 'RFBUYER', 'SMARTSFCOCO', 'RF').required(),
  groupType: Joi.string().valid('FOFO', 'COCO', 'FOCO').required(),
  name: Joi.string().min(2).max(100).required(),
  email: Joi.string().email().optional(),
  mobile: Joi.string().pattern(/^[6-9]\d{9}$/).required(),
  altMobile: Joi.string().pattern(/^[6-9]\d{9}$/).optional(),
  gstNumber: Joi.string().pattern(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/).optional(),
  sizeSqFt: Joi.number().positive().optional(),
  ownershipType: Joi.string().valid('Owned', 'Rented').default('Owned'),
  rentAgreementUrl: Joi.string().uri().optional(),
  addressLine1: Joi.string().min(5).max(200).required(),
  addressLine2: Joi.string().max(200).optional(),
  city: Joi.string().min(2).max(50).required(),
  district: Joi.string().max(50).optional(),
  state: Joi.string().min(2).max(50).required(),
  pincode: Joi.string().pattern(/^[1-9][0-9]{5}$/).required(),
  latitude: Joi.number().min(-90).max(90).optional(),
  longitude: Joi.number().min(-180).max(180).optional(),
  ownerDetails: Joi.object({
    name: Joi.string().min(2).max(100).required(),
    aadharNumber: Joi.string().pattern(/^[0-9]{12}$/).optional(),
    panNumber: Joi.string().pattern(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/).optional(),
    idProofUrl: Joi.string().uri().optional(),
    photoUrl: Joi.string().uri().optional()
  }).required(),
  documents: Joi.array().items(Joi.object({
    type: Joi.string().valid('Aadhar', 'PAN', 'GST', 'RentAgreement', 'ShopPhoto', 'BankPassbook', 'BusinessProof').required(),
    refNumber: Joi.string().optional(),
    fileUrl: Joi.string().uri().required(),
    issuedBy: Joi.string().optional(),
    issuedDate: Joi.date().optional(),
    verified: Joi.boolean().default(false)
  })).optional(),
  kycStatus: Joi.object({
    businessProofVerified: Joi.boolean().default(false),
    gstVerified: Joi.boolean().default(false),
    addressVerified: Joi.boolean().default(false),
    panVerified: Joi.boolean().default(false),
    aadharVerified: Joi.boolean().default(false),
    overallStatus: Joi.string().valid('Pending', 'Under Review', 'Approved', 'Rejected', 'Partial Approval').default('Pending')
  }).optional()
});

// Update franchise validation schema
const updateFranchiseSchema = Joi.object({
  type: Joi.string().valid('SF', 'RF').optional(),
  subType: Joi.string().valid('SFSELLER', 'SFBUYER', 'RFSELLER', 'RFBUYER', 'SMARTSFCOCO', 'RF').optional(),
  groupType: Joi.string().valid('FOFO', 'COCO', 'FOCO').optional(),
  name: Joi.string().min(2).max(100).optional(),
  email: Joi.string().email().optional(),
  mobile: Joi.string().pattern(/^[6-9]\d{9}$/).optional(),
  altMobile: Joi.string().pattern(/^[6-9]\d{9}$/).optional(),
  gstNumber: Joi.string().pattern(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/).optional(),
  sizeSqFt: Joi.number().positive().optional(),
  ownershipType: Joi.string().valid('Owned', 'Rented').optional(),
  rentAgreementUrl: Joi.string().uri().optional(),
  addressLine1: Joi.string().min(5).max(200).optional(),
  addressLine2: Joi.string().max(200).optional(),
  city: Joi.string().min(2).max(50).optional(),
  district: Joi.string().max(50).optional(),
  state: Joi.string().min(2).max(50).optional(),
  pincode: Joi.string().pattern(/^[1-9][0-9]{5}$/).optional(),
  latitude: Joi.number().min(-90).max(90).optional(),
  longitude: Joi.number().min(-180).max(180).optional(),
  ownerDetails: Joi.object({
    name: Joi.string().min(2).max(100).optional(),
    aadharNumber: Joi.string().pattern(/^[0-9]{12}$/).optional(),
    panNumber: Joi.string().pattern(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/).optional(),
    idProofUrl: Joi.string().uri().optional(),
    photoUrl: Joi.string().uri().optional()
  }).optional()
});

// KYC status update validation schema
const updateKycStatusSchema = Joi.object({
  businessProofVerified: Joi.boolean().optional(),
  gstVerified: Joi.boolean().optional(),
  addressVerified: Joi.boolean().optional(),
  panVerified: Joi.boolean().optional(),
  aadharVerified: Joi.boolean().optional(),
  overallStatus: Joi.string().valid('Pending', 'Under Review', 'Approved', 'Rejected', 'Partial Approval').required(),
  remarks: Joi.string().max(500).optional()
});

// Add document validation schema
const addDocumentSchema = Joi.object({
  type: Joi.string().valid('Aadhar', 'PAN', 'GST', 'RentAgreement', 'ShopPhoto', 'BankPassbook', 'BusinessProof').required(),
  refNumber: Joi.string().optional(),
  fileUrl: Joi.string().uri().required(),
  issuedBy: Joi.string().optional(),
  issuedDate: Joi.date().optional(),
  verified: Joi.boolean().default(false)
});

// Update document verification schema
const updateDocumentVerificationSchema = Joi.object({
  verified: Joi.boolean().required(),
  remarks: Joi.string().max(500).optional()
});

// Location search validation schema
const locationSearchSchema = Joi.object({
  latitude: Joi.number().min(-90).max(90).required(),
  longitude: Joi.number().min(-180).max(180).required(),
  radius: Joi.number().positive().max(100).default(10)
});

// Search validation schema
const searchSchema = Joi.object({
  q: Joi.string().min(1).max(100).required(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10)
});

// Pagination and filter validation schema
const paginationFilterSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10),
  type: Joi.string().valid('SF', 'RF').optional(),
  subType: Joi.string().valid('SFSELLER', 'SFBUYER', 'RFSELLER', 'RFBUYER', 'SMARTSFCOCO', 'RF').optional(),
  groupType: Joi.string().valid('FOFO', 'COCO', 'FOCO').optional(),
  city: Joi.string().min(2).max(50).optional(),
  state: Joi.string().min(2).max(50).optional(),
  kycStatus: Joi.string().valid('Pending', 'Under Review', 'Approved', 'Rejected', 'Partial Approval').optional(),
  mobile: Joi.string().pattern(/^[6-9]\d{9}$/).optional(),
  email: Joi.string().email().optional()
});

// Validation middleware factory
const validate = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body);
    
    if (error) {
      return res.status(400).json({
        success: false,
        error: 'ValidationError',
        message: 'Validation failed',
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      });
    }
    
    req.body = value;
    next();
  };
};

// Query validation middleware factory
const validateQuery = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.query);
    
    if (error) {
      return res.status(400).json({
        success: false,
        error: 'ValidationError',
        message: 'Query validation failed',
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      });
    }
    
    req.query = value;
    next();
  };
};

module.exports = {
  createFranchiseSchema,
  updateFranchiseSchema,
  updateKycStatusSchema,
  addDocumentSchema,
  updateDocumentVerificationSchema,
  locationSearchSchema,
  searchSchema,
  paginationFilterSchema,
  validate,
  validateQuery
}; 