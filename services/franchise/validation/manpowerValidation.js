const Joi = require('joi');

// Validation schema for creating manpower
const createManpowerSchema = Joi.object({
  referenceId: Joi.string().optional(),
  type: Joi.string().valid('StoreEmployee', 'DeliveryPerson', 'DeliveryAgent', 'Picker', 'ManpowerLead').required(),
  isDeliveryPerson: Joi.boolean().optional(),
  isAvailable: Joi.boolean().optional(),
  name: Joi.string().required(),
  gender: Joi.string().optional(),
  dob: Joi.string().optional(),
  vehicleNo: Joi.string().optional(),
  mobileNo: Joi.number().optional(),
  emailId: Joi.string().email().optional(),
  address: Joi.object({
    door_no: Joi.string().optional(),
    street: Joi.string().optional(),
    full_addrees: Joi.string().optional(),
    landmark: Joi.string().optional()
  }).optional(),
  state: Joi.string().optional(),
  town: Joi.string().optional(),
  district: Joi.string().optional(),
  pincode: Joi.number().optional(),
  status: Joi.string().optional(),
  services: Joi.object({
    pos: Joi.object({
      active: Joi.boolean().optional()
    }).optional(),
    dih: Joi.object({
      active: Joi.boolean().optional()
    }).optional()
  }).optional(),
  customerId: Joi.string().optional(),
  userId: Joi.string().optional(),
  documentsRequired: Joi.object({
    business: Joi.array().items(Joi.object({
      name: Joi.string().optional(),
      docType: Joi.string().optional(),
      refNo: Joi.string().optional(),
      assetId: Joi.string().optional()
    })).optional(),
    address: Joi.array().items(Joi.object({
      name: Joi.string().optional(),
      docType: Joi.string().optional(),
      refNo: Joi.string().optional(),
      refNoHashKey: Joi.string().optional(),
      assetId: Joi.string().optional(),
      documentFace: Joi.string().valid('Front', 'Back').optional()
    })).optional(),
    photo: Joi.array().items(Joi.object({
      name: Joi.string().optional(),
      docType: Joi.string().optional(),
      refNo: Joi.string().optional(),
      refNoHashKey: Joi.string().optional(),
      assetId: Joi.string().optional(),
      documentFace: Joi.string().valid('Front', 'Back').optional()
    })).optional()
  }).optional(),
  profileImages: Joi.array().items(Joi.string()).optional(),
  deviceInfo: Joi.object({
    deviceId: Joi.string().optional(),
    deviceName: Joi.string().optional(),
    deviceRefNo: Joi.string().optional()
  }).optional(),
  employeeAccess: Joi.object({
    enableGRNProcessing: Joi.boolean().optional(),
    enablePickingManagement: Joi.boolean().optional(),
    canPlaceB2BOrder: Joi.boolean().optional(),
    managePickPackOperationSetting: Joi.boolean().optional(),
    manageEmployeeAccess: Joi.boolean().optional(),
    manageEmployees: Joi.boolean().optional(),
    managePickerDevice: Joi.boolean().optional(),
    manageReturnInward: Joi.boolean().optional(),
    attendance: Joi.boolean().optional(),
    manageManpowerLeads: Joi.boolean().optional()
  }).optional(),
  isActive: Joi.boolean().optional()
});

// Validation schema for updating manpower
const updateManpowerSchema = Joi.object({
  referenceId: Joi.string().optional(),
  type: Joi.string().valid('StoreEmployee', 'DeliveryPerson', 'DeliveryAgent', 'Picker', 'ManpowerLead').optional(),
  isDeliveryPerson: Joi.boolean().optional(),
  isAvailable: Joi.boolean().optional(),
  name: Joi.string().optional(),
  gender: Joi.string().optional(),
  dob: Joi.string().optional(),
  vehicleNo: Joi.string().optional(),
  mobileNo: Joi.number().optional(),
  emailId: Joi.string().email().optional(),
  address: Joi.object({
    door_no: Joi.string().optional(),
    street: Joi.string().optional(),
    full_addrees: Joi.string().optional(),
    landmark: Joi.string().optional()
  }).optional(),
  state: Joi.string().optional(),
  town: Joi.string().optional(),
  district: Joi.string().optional(),
  pincode: Joi.number().optional(),
  status: Joi.string().optional(),
  services: Joi.object({
    pos: Joi.object({
      active: Joi.boolean().optional()
    }).optional(),
    dih: Joi.object({
      active: Joi.boolean().optional()
    }).optional()
  }).optional(),
  customerId: Joi.string().optional(),
  userId: Joi.string().optional(),
  documentsRequired: Joi.object({
    business: Joi.array().items(Joi.object({
      name: Joi.string().optional(),
      docType: Joi.string().optional(),
      refNo: Joi.string().optional(),
      assetId: Joi.string().optional()
    })).optional(),
    address: Joi.array().items(Joi.object({
      name: Joi.string().optional(),
      docType: Joi.string().optional(),
      refNo: Joi.string().optional(),
      refNoHashKey: Joi.string().optional(),
      assetId: Joi.string().optional(),
      documentFace: Joi.string().valid('Front', 'Back').optional()
    })).optional(),
    photo: Joi.array().items(Joi.object({
      name: Joi.string().optional(),
      docType: Joi.string().optional(),
      refNo: Joi.string().optional(),
      refNoHashKey: Joi.string().optional(),
      assetId: Joi.string().optional(),
      documentFace: Joi.string().valid('Front', 'Back').optional()
    })).optional()
  }).optional(),
  profileImages: Joi.array().items(Joi.string()).optional(),
  deviceInfo: Joi.object({
    deviceId: Joi.string().optional(),
    deviceName: Joi.string().optional(),
    deviceRefNo: Joi.string().optional()
  }).optional(),
  employeeAccess: Joi.object({
    enableGRNProcessing: Joi.boolean().optional(),
    enablePickingManagement: Joi.boolean().optional(),
    canPlaceB2BOrder: Joi.boolean().optional(),
    managePickPackOperationSetting: Joi.boolean().optional(),
    manageEmployeeAccess: Joi.boolean().optional(),
    manageEmployees: Joi.boolean().optional(),
    managePickerDevice: Joi.boolean().optional(),
    manageReturnInward: Joi.boolean().optional(),
    attendance: Joi.boolean().optional(),
    manageManpowerLeads: Joi.boolean().optional()
  }).optional(),
  isActive: Joi.boolean().optional()
});

// Validation schema for query parameters
const querySchema = Joi.object({
  page: Joi.number().integer().min(1).optional(),
  limit: Joi.number().integer().min(1).max(100).optional(),
  type: Joi.string().valid('StoreEmployee', 'DeliveryPerson', 'DeliveryAgent', 'Picker', 'ManpowerLead').optional(),
  isDeliveryPerson: Joi.boolean().optional(),
  isAvailable: Joi.boolean().optional(),
  status: Joi.string().optional(),
  isActive: Joi.boolean().optional(),
  name: Joi.string().optional(),
  mobileNo: Joi.number().optional(),
  emailId: Joi.string().email().optional()
});

module.exports = {
  createManpowerSchema,
  updateManpowerSchema,
  querySchema
}; 