const mongoose = require('mongoose');

const franchiseSchema = new mongoose.Schema({
  franchiseId: { type: String, required: true, unique: true },
  type: { type: String, enum: ['SF', 'RF'], required: true },
  subType: {
    type: String,
    enum: ['SFSELLER', 'SFBUYER', 'RFSELLER', 'RFBUYER','SMARTSFCOCO','RF'],
    required: true
  },
  groupType: {
    type: String,
    enum: ['FOFO', 'COCO', 'FOCO'],
    required: true
  },

  name: { type: String, required: true }, // business name
  email: { type: String, lowercase: true },
  mobile: { type: String, required: true, unique: true },
  altMobile: { type: String },

  gstNumber: { type: String },
  sizeSqFt: { type: Number },
  ownershipType: { type: String, enum: ['Owned', 'Rented'], default: 'Owned' },
  rentAgreementUrl: { type: String },

  addressLine1: { type: String, required: true },
  addressLine2: { type: String },
  city: { type: String, required: true },
  district: { type: String },
  state: { type: String, required: true },
  pincode: { type: String, required: true },

  latitude: { type: Number },
  longitude: { type: Number },

  ownerDetails: {
    name: { type: String, required: true },
    aadharNumber: { type: String },
    panNumber: { type: String },
    idProofUrl: { type: String },
    photoUrl: { type: String },
  },

  documents: [{
    type: {
      type: String,
      enum: ['Aadhar', 'PAN', 'GST', 'RentAgreement', 'ShopPhoto', 'BankPassbook', 'BusinessProof'],
      required: true
    },
    refNumber: { type: String },
    fileUrl: { type: String, required: true },
    issuedBy: { type: String },
    issuedDate: { type: Date },
    verified: { type: Boolean, default: false },
    uploadedOn: { type: Date, default: Date.now }
  }],

  kycStatus: {
    businessProofVerified: { type: Boolean, default: false },
    gstVerified: { type: Boolean, default: false },
    addressVerified: { type: Boolean, default: false },
    panVerified: { type: Boolean, default: false },
    aadharVerified: { type: Boolean, default: false },
    overallStatus: {
      type: String,
      enum: ['Pending', 'Under Review', 'Approved', 'Rejected','Partial Approval'],
      default: 'Pending'
    }
  },

  statusLogs: [{
    status: {
      type: String,
      enum: ['Pending', 'Under Review', 'Approved', 'Rejected','Partial Approval']
    },
    updatedBy: {
      id: { type: String },
      name: { type: String },
      role: { type: String }
    },
    remarks: { type: String },
    updatedOn: { type: Date, default: Date.now }
  }],

  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Update the updatedAt field before saving
franchiseSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Update the updatedAt field before updating
franchiseSchema.pre('findOneAndUpdate', function() {
  this.set({ updatedAt: new Date() });
});

module.exports = mongoose.model('Franchise', franchiseSchema); 