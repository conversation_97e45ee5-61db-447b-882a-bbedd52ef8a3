const mongoose = require('mongoose');

const manpowerSchema = new mongoose.Schema({
    _id: { type: String },
    referenceId: { type: String },
    type:{
        type: String, enum: ["StoreEmployee", "Delivery<PERSON><PERSON>","DeliveryAgent","Picker","ManpowerLead"]
    },
    isDeliveryPerson:{ type: Boolean,default:false},
    isAvailable:{ type: Boolean,default:true},
    name: { type: String,required:true },
    parentFranchiseInfo: {
        id: { type: String },
        name: { type: String },
        mobile: { type: String },
        address: {
            door_no: { type: String },
            street: { type: String },
            full_address: { type: String },
            landmark: { type: String }
        },
        type: { type: String },
        state: { type: String },
        district: { type: String },
        city: { type: String },
        town: { type: String },
        pincode: { type: String },
        subType: { type: String },
        email: String,
        sellerId: { type: String },
        gstNo: { type: String }
    },
    gender: { type: String },
    dob: { type: String },
    vehicleNo: { type: String },
    mobileNo: { type: Number },
    emailId: { type: String },
    address: {
        door_no: { type: String },
        street: { type: String },
        full_addrees: { type: String },
        landmark: { type: String }
    },
    state: { type: String },
    town: { type: String },
    district: { type: String },
    pincode: { type: Number },
    status: {
        type: String,
        default: "Created"
    },
    logs: [{
        status: { type: String },
        remarks: { type: String },
        createdAt: { type: Date }
    }],
    services: {
        pos: { active: { type: Boolean, default: true } },
        dih: { active: { type: Boolean, default: false } }
    },
    customerId: { type: String },
    userId: { type: String },
    documentsRequired: {
        business: [
            {
                name: { type: String },
                docType: { type: String, default: "business" },
                refNo: { type: String },
                assetId: { type: String }
            }
        ],
        address: [
            {
                name: { type: String },
                docType: { type: String, default: "address" },
                refNo: { type: String },
                refNoHashKey: { type: String },
                assetId: { type: String },
                documentFace: { type: String, enum: ["Front", "Back"] }
            }
        ],
        photo: [
            {
                name: { type: String },
                docType: { type: String, default: "photo" },
                refNo: { type: String },
                refNoHashKey: { type: String },
                assetId: { type: String },
                documentFace: { type: String, enum: ["Front", "Back"] }
            }
        ]
    },
    profileImages: [
        { type: String }
    ],
    deviceInfo: {
        deviceId: { type: String },
        deviceName: { type: String },
        deviceRefNo: { type: String },
    },
    employeeAccess: {
        enableGRNProcessing: { type: Boolean,default:false },
        enablePickingManagement: { type: Boolean,default:false },
        canPlaceB2BOrder: { type: Boolean,default:false },
        managePickPackOperationSetting:{ type: Boolean,default:false},
        manageEmployeeAccess:{ type: Boolean,default:false},
        manageEmployees: { type: Boolean,default:false},
        managePickerDevice:{ type: Boolean,default:false},
        manageReturnInward:{ type: Boolean,default:false},
        attendance:{ type: Boolean,default:false},
        manageManpowerLeads:{ type: Boolean,default:false}
    },
    isActive: { type: Boolean, default: true },
    deleted: {
        type: Boolean,
        default: false
    },
    createdAt: { type: Date },
    lastUpdated: { type: Date }
});



module.exports = mongoose.model('Manpower', manpowerSchema); 