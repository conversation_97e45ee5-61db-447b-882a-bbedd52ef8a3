# Franchise Service

A microservice for managing franchise operations, built with Node.js, Express, and MongoDB.

## Features

- **CRUD Operations**: Create, read, update, and delete franchises
- **KYC Management**: Handle franchise KYC verification and status updates
- **Document Management**: Upload and verify franchise documents
- **Location-based Search**: Find franchises within a specified radius
- **Advanced Filtering**: Filter franchises by type, location, status, etc.
- **Pagination**: Efficient data retrieval with pagination support
- **Statistics**: Get franchise statistics and analytics
- **Health Monitoring**: Comprehensive health check endpoints
- **Error Handling**: Centralized error handling with custom error classes
- **Logging**: Request/response logging with performance monitoring
- **Security**: Rate limiting, CORS, and security headers

## API Endpoints

### Franchise Management

- `GET /franchises` - Get all franchises with pagination and filters
- `GET /franchises/search` - Search franchises by name, mobile, email, etc.
- `GET /franchises/stats` - Get franchise statistics
- `GET /franchises/location` - Get franchises by location (latitude/longitude)
- `POST /franchises` - Create new franchise
- `GET /franchises/:franchiseId` - Get franchise by ID
- `PUT /franchises/:franchiseId` - Update franchise
- `DELETE /franchises/:franchiseId` - Delete franchise

### KYC Management

- `PUT /franchises/:franchiseId/kyc` - Update KYC status

### Document Management

- `POST /franchises/:franchiseId/documents` - Add document to franchise
- `PUT /franchises/:franchiseId/documents/:documentId` - Update document verification

### Health & Monitoring

- `GET /health` - Basic health check
- `GET /health/detailed` - Detailed health check with database connectivity
- `GET /health/ready` - Kubernetes readiness probe
- `GET /health/live` - Kubernetes liveness probe
- `GET /metrics` - Service metrics

## Installation

1. Install dependencies:
```bash
npm install
```

2. Set up environment variables:
```bash
cp .env.example .env
```

3. Configure the following environment variables:
```env
# Server Configuration
PORT=5003
NODE_ENV=development

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/franchise

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Request Configuration
REQUEST_SIZE_LIMIT=10mb
```

## Usage

### Development
```bash
npm run dev
```

### Production
```bash
npm start
```

### PM2 Management
```bash
# Start service
npm run pm2:start

# Stop service
npm run pm2:stop

# Restart service
npm run pm2:restart

# View logs
npm run pm2:logs

# Monitor
npm run pm2:monit
```

## Database Schema

### Franchise Model

```javascript
{
  franchiseId: String,           // Unique franchise identifier
  type: String,                  // 'SF' or 'RF'
  subType: String,               // 'SFSELLER', 'SFBUYER', etc.
  groupType: String,             // 'FOFO', 'COCO', 'FOCO'
  name: String,                  // Business name
  email: String,                 // Email address
  mobile: String,                // Mobile number
  altMobile: String,             // Alternative mobile
  gstNumber: String,             // GST number
  sizeSqFt: Number,              // Store size in sq ft
  ownershipType: String,         // 'Owned' or 'Rented'
  rentAgreementUrl: String,      // Rent agreement URL
  addressLine1: String,          // Address line 1
  addressLine2: String,          // Address line 2
  city: String,                  // City
  district: String,              // District
  state: String,                 // State
  pincode: String,               // Pincode
  latitude: Number,              // Latitude
  longitude: Number,             // Longitude
  ownerDetails: {                // Owner information
    name: String,
    aadharNumber: String,
    panNumber: String,
    idProofUrl: String,
    photoUrl: String
  },
  documents: [{                  // Document array
    type: String,                // Document type
    refNumber: String,           // Reference number
    fileUrl: String,             // File URL
    issuedBy: String,            // Issuing authority
    issuedDate: Date,            // Issue date
    verified: Boolean,           // Verification status
    uploadedOn: Date             // Upload date
  }],
  kycStatus: {                   // KYC status
    businessProofVerified: Boolean,
    gstVerified: Boolean,
    addressVerified: Boolean,
    panVerified: Boolean,
    aadharVerified: Boolean,
    overallStatus: String        // 'Pending', 'Under Review', 'Approved', 'Rejected', 'Partial Approval'
  },
  statusLogs: [{                 // Status change logs
    status: String,
    updatedBy: {
      id: String,
      name: String,
      role: String
    },
    remarks: String,
    updatedOn: Date
  }],
  createdAt: Date,               // Creation timestamp
  updatedAt: Date                // Last update timestamp
}
```

## API Examples

### Create Franchise
```bash
curl -X POST http://localhost:5003/franchises \
  -H "Content-Type: application/json" \
  -d '{
    "type": "SF",
    "subType": "SFSELLER",
    "groupType": "FOFO",
    "name": "ABC Store",
    "mobile": "9876543210",
    "addressLine1": "123 Main Street",
    "city": "Mumbai",
    "state": "Maharashtra",
    "pincode": "400001",
    "ownerDetails": {
      "name": "John Doe"
    }
  }'
```

### Get Franchises with Filters
```bash
curl "http://localhost:5003/franchises?type=SF&city=Mumbai&page=1&limit=10"
```

### Update KYC Status
```bash
curl -X PUT http://localhost:5003/franchises/FR123456/kyc \
  -H "Content-Type: application/json" \
  -d '{
    "overallStatus": "Approved",
    "businessProofVerified": true,
    "gstVerified": true,
    "remarks": "All documents verified successfully"
  }'
```

### Search Franchises
```bash
curl "http://localhost:5003/franchises/search?q=ABC&page=1&limit=10"
```

## Error Handling

The service uses centralized error handling with custom error classes:

- `ValidationError` (400) - Invalid input data
- `AuthenticationError` (401) - Authentication failed
- `AuthorizationError` (403) - Access denied
- `NotFoundError` (404) - Resource not found
- `ConflictError` (409) - Resource conflict
- `RateLimitError` (429) - Too many requests
- `DatabaseError` (500) - Database operation failed

## Testing

```bash
# Run tests
npm test

# Run tests in watch mode
npm run test:watch
```

## Monitoring

The service provides comprehensive monitoring endpoints:

- **Health Check**: Basic service health
- **Detailed Health**: Database connectivity and model access
- **Metrics**: Business and system metrics
- **Performance**: Request duration monitoring
- **Logging**: Request/response logging with sensitive data redaction

## Security Features

- **Rate Limiting**: Configurable request rate limiting
- **CORS**: Cross-origin resource sharing configuration
- **Security Headers**: XSS protection, content type options
- **Request Size Limiting**: Configurable request size limits
- **Input Validation**: Comprehensive input validation
- **Error Sanitization**: Production error message sanitization

## Dependencies

- **express**: Web framework
- **mongoose**: MongoDB ODM
- **cors**: Cross-origin resource sharing
- **helmet**: Security headers
- **express-rate-limit**: Rate limiting
- **morgan**: HTTP request logger
- **uuid**: Unique identifier generation
- **dotenv**: Environment variable management
- **serverless-http**: Serverless deployment support

## License

ISC 