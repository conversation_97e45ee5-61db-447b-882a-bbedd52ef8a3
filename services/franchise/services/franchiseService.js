const Franchise = require('../models/Franchise');
const { v4: uuidv4 } = require('uuid');

/**
 * Franchise Service
 * Handles all business logic for franchise operations
 */

/**
 * Get franchise by ID
 */
const getFranchiseById = async (franchiseId) => {
  try {
    const franchise = await Franchise.findOne({ franchiseId: franchiseId });
    return franchise;
  } catch (error) {
    throw new Error(`Error fetching franchise: ${error.message}`);
  }
};



/**
 * Get all franchises with pagination and filters
 */
const getAllFranchises = async (filters = {}, page = 1, limit = 10) => {
  try {
    const skip = (page - 1) * limit;
    
    // Build filter object
    const filterObj = {};
    
    if (filters.type) filterObj.type = filters.type;
    if (filters.subType) filterObj.subType = filters.subType;
    if (filters.groupType) filterObj.groupType = filters.groupType;
    if (filters.city) filterObj.city = { $regex: filters.city, $options: 'i' };
    if (filters.state) filterObj.state = { $regex: filters.state, $options: 'i' };
    if (filters.kycStatus) filterObj['kycStatus.overallStatus'] = filters.kycStatus;
    if (filters.mobile) filterObj.mobile = { $regex: filters.mobile, $options: 'i' };
    if (filters.email) filterObj.email = { $regex: filters.email, $options: 'i' };

    const franchises = await Franchise.find(filterObj)
      .skip(skip)
      .limit(limit)
      .sort({ createdAt: -1 });

    const total = await Franchise.countDocuments(filterObj);

    return {
      franchises,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    throw new Error(`Error fetching franchises: ${error.message}`);
  }
};

/**
 * Create new franchise
 */
const createFranchise = async (franchiseData) => {
  try {
    const result = await Franchise.find({ franchiseId: franchiseData.franchiseId });
    if (result.length > 0) return result;
    const franchise = new Franchise(franchiseData);
    const savedFranchise = await franchise.save();
    return savedFranchise;
  } catch (error) {
    if (error.code === 11000) {
      const field = Object.keys(error.keyValue)[0];
      throw new Error(`${field} already exists`);
    }
    throw new Error(`Error creating franchise: ${error.message}`);
  }
};



module.exports = {
  getFranchiseById,
  getAllFranchises,
  createFranchise
}; 