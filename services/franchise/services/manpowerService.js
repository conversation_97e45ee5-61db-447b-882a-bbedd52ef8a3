const Manpower = require('../models/Manpower');
const Franchise = require('../models/Franchise');

/**
 * Create new manpower
 */
const createManpower = async (manpowerData, parentFranchiseId) => {
  try {
    // Fetch parent franchise info
    const parentFranchise = await Franchise.findById(parentFranchiseId);
    if (!parentFranchise) {
      throw new Error('Parent franchise not found');
    }

    // Prepare parent franchise info
    const parentFranchiseInfo = {
      id: parentFranchise._id,
      name: parentFranchise.name,
      mobile: parentFranchise.mobile,
      address: parentFranchise.address,
      type: parentFranchise.type,
      state: parentFranchise.state,
      district: parentFranchise.district,
      city: parentFranchise.city,
      town: parentFranchise.town,
      pincode: parentFranchise.pincode,
      subType: parentFranchise.subType,
      email: parentFranchise.email,
      sellerId: parentFranchise.sellerId,
      gstNo: parentFranchise.gstNo
    };

    // Create manpower with parent franchise info
    const manpower = new Manpower({
      ...manpowerData,
      parentFranchiseInfo,
      createdAt: new Date(),
      lastUpdated: new Date()
    });

    const savedManpower = await manpower.save();
    return savedManpower;
  } catch (error) {
    throw error;
  }
};

/**
 * Get manpower by ID
 */
const getManpowerById = async (manpowerId, parentFranchiseId) => {
  try {
    const manpower = await Manpower.findOne({
      _id: manpowerId,
      'parentFranchiseInfo.id': parentFranchiseId,
      deleted: false
    });

    if (!manpower) {
      throw new Error('Manpower not found');
    }

    return manpower;
  } catch (error) {
    throw error;
  }
};

/**
 * Get all manpower for a franchise with pagination and filters
 */
const getAllManpower = async (parentFranchiseId, filters = {}, page = 1, limit = 10) => {
  try {
    const query = {
      'parentFranchiseInfo.id': parentFranchiseId,
      deleted: false
    };

    // Add filters
    if (filters.type) query.type = filters.type;
    if (filters.isDeliveryPerson !== undefined) query.isDeliveryPerson = filters.isDeliveryPerson;
    if (filters.isAvailable !== undefined) query.isAvailable = filters.isAvailable;
    if (filters.status) query.status = filters.status;
    if (filters.isActive !== undefined) query.isActive = filters.isActive;
    if (filters.name) query.name = { $regex: filters.name, $options: 'i' };
    if (filters.mobileNo) query.mobileNo = filters.mobileNo;
    if (filters.emailId) query.emailId = { $regex: filters.emailId, $options: 'i' };

    const skip = (page - 1) * limit;

    const [manpower, total] = await Promise.all([
      Manpower.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      Manpower.countDocuments(query)
    ]);

    return {
      manpower,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Update manpower
 */
const updateManpower = async (manpowerId, updateData, parentFranchiseId) => {
  try {
    const manpower = await Manpower.findOne({
      _id: manpowerId,
      'parentFranchiseInfo.id': parentFranchiseId,
      deleted: false
    });

    if (!manpower) {
      throw new Error('Manpower not found');
    }

    // Update manpower data
    Object.assign(manpower, updateData, {
      lastUpdated: new Date()
    });

    const updatedManpower = await manpower.save();
    return updatedManpower;
  } catch (error) {
    throw error;
  }
};

/**
 * Delete manpower (soft delete)
 */
const deleteManpower = async (manpowerId, parentFranchiseId) => {
  try {
    const manpower = await Manpower.findOne({
      _id: manpowerId,
      'parentFranchiseInfo.id': parentFranchiseId,
      deleted: false
    });

    if (!manpower) {
      throw new Error('Manpower not found');
    }

    manpower.deleted = true;
    manpower.lastUpdated = new Date();

    const deletedManpower = await manpower.save();
    return deletedManpower;
  } catch (error) {
    throw error;
  }
};

module.exports = {
  createManpower,
  getManpowerById,
  getAllManpower,
  updateManpower,
  deleteManpower
}; 