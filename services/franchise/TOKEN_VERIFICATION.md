# Token Verification in Franchise Service

## Overview

The Franchise Service now includes authentication middleware that verifies JWT tokens by calling the User Service's verify-token endpoint. This ensures that all manpower operations are properly authenticated.

## How It Works

### 1. Authentication Middleware (`middleware/auth.js`)

The authentication middleware:
- Extracts the JWT token from the `Authorization` header (must start with "JWT ")
- Calls the User Service's `/auth/verify-token` endpoint to validate the token
- Extracts user data from the response and attaches it to `req.user`
- Handles various error scenarios (network errors, timeouts, invalid tokens)

### 2. User Data Structure

After successful token verification, the following user data is attached to `req.user`:

```javascript
{
  _id: "653a1e97c07208177f17edae",
  username: "7204206740",
  isActive: true,
  sellerId: "SEL1824",
  isSeller: true,
  franchiseId: "F323969", // This is the franchise ID from the token
  userType: "<PERSON><PERSON>",
  name: "<PERSON> <PERSON><PERSON><PERSON>a",
  userSubType: "RF"
}
```

### 3. Manpower Controller Integration

The manpower controller uses `req.user.franchiseId` to ensure that users can only access manpower data for their own franchise:

```javascript
const parentFranchiseId = req.user.franchiseId; // Get from token
```

## Configuration

### Environment Variables

Set the following environment variable in your `.env` file:

```env
USER_SERVICE_URL=http://localhost:5001
```

### Service Dependencies

- **User Service**: Must be running on the configured URL
- **Axios**: Already included in package.json for HTTP requests

## API Endpoints

All manpower endpoints now require authentication:

- `GET /manpower` - Get all manpower (requires token)
- `GET /manpower/:manpowerId` - Get specific manpower (requires token)
- `POST /manpower` - Create new manpower (requires token)
- `PUT /manpower/:manpowerId` - Update manpower (requires token)
- `DELETE /manpower/:manpowerId` - Delete manpower (requires token)

## Request Format

Include the JWT token in the Authorization header:

```
Authorization: JWT your_jwt_token_here
```

## Error Responses

### 401 Unauthorized
- Missing or invalid Authorization header
- Token verification failed
- User service authentication error

### 503 Service Unavailable
- User service is not available
- Network connection issues

### 408 Request Timeout
- Token verification request timed out

### 500 Internal Server Error
- Unexpected errors in authentication middleware

## Testing

Use the provided test script to verify token verification:

```bash
cd services/franchise
node test-token-verification.js
```

## Security Features

1. **Token Format Validation**: Only accepts tokens starting with "JWT "
2. **Timeout Protection**: 10-second timeout for token verification requests
3. **Error Handling**: Comprehensive error handling for various failure scenarios
4. **Franchise Isolation**: Users can only access data for their own franchise
5. **Logging**: Request IDs and authentication events are logged

## Troubleshooting

### Common Issues

1. **"Authentication service unavailable"**
   - Check if User Service is running
   - Verify USER_SERVICE_URL environment variable

2. **"Token verification failed"**
   - Ensure token is valid and not expired
   - Check if token exists in User Service database

3. **"Invalid Authorization header"**
   - Ensure token starts with "JWT " (note the space)
   - Check token format

### Debug Mode

Enable debug logging by setting:

```env
NODE_ENV=development
```

This will show detailed authentication logs in the console. 