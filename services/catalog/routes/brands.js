const express = require('express');
const router = express.Router();
const brandController = require('../controller/brandController');

/**
 * Brand Routes
 * Handles routing for brand management operations
 */

/**
 * @route   POST /brands
 * @desc    Create a new brand
 * @access  Private
 */
router.post('/', brandController.createBrand);

/**
 * @route   GET /brands
 * @desc    Get all brands with pagination and filtering
 * @access  Public
 */
router.get('/', brandController.getBrands);

/**
 * @route   GET /brands/active
 * @desc    Get active brands
 * @access  Public
 */
router.get('/active', brandController.getActiveBrands);

/**
 * @route   GET /brands/master
 * @desc    Get master brands
 * @access  Public
 */
router.get('/master', brandController.getMasterBrands);

/**
 * @route   GET /brands/category/:categoryId
 * @desc    Get brands by category
 * @access  Public
 */
router.get('/category/:categoryId', brandController.getBrandsByCategory);

/**
 * @route   GET /brands/:id
 * @desc    Get brand by ID
 * @access  Public
 */
router.get('/:id', brandController.getBrandById);

/**
 * @route   PUT /brands/:id
 * @desc    Update brand by ID
 * @access  Private
 */
router.put('/:id', brandController.updateBrand);

/**
 * @route   PATCH /brands/:id/status
 * @desc    Update brand status
 * @access  Private
 */
router.patch('/:id/status', brandController.updateBrandStatus);

/**
 * @route   DELETE /brands/:id
 * @desc    Delete brand by ID (soft delete)
 * @access  Private
 */
router.delete('/:id', brandController.deleteBrand);

module.exports = router;
