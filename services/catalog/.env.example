# User Service Configuration

# Server Configuration
PORT=5001
NODE_ENV=development

# Database Configuration
MONGODB_URI=mongodb://skadmin:xmuWj78KdYh8SHZt@*************:27017/skstore?authSource=admin

# JWT Configuration (should match API Gateway)
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=24h

# Service Configuration
SERVICE_NAME=catalog-service
SERVICE_VERSION=1.0.0
API_VERSION=1.0.0

# Frontend URL for CORS
FRONTEND_URL=http://localhost:3000

# Logging Configuration
LOG_LEVEL=info

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Authentication Rate Limiting
AUTH_RATE_LIMIT_WINDOW_MS=900000
AUTH_RATE_LIMIT_MAX_REQUESTS=5

# Request Configuration
REQUEST_SIZE_LIMIT=10mb
REQUEST_TIMEOUT_MS=30000

# Pagination Defaults
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100

# Security Configuration
BCRYPT_SALT_ROUNDS=12
PASSWORD_MIN_LENGTH=8

# Session Configuration
SESSION_CLEANUP_INTERVAL=3600000
MAX_SESSIONS_PER_USER=5

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30000

# Performance Monitoring
SLOW_REQUEST_THRESHOLD=1000

# Database Configuration
DB_CONNECTION_TIMEOUT=5000
DB_SOCKET_TIMEOUT=45000
DB_MAX_POOL_SIZE=10

# Error Handling
STACK_TRACE_LIMIT=10

# Feature Flags
ENABLE_2FA=true
ENABLE_SESSION_MANAGEMENT=true
ENABLE_CATALOG_SEARCH=true
ENABLE_CATALOG_STATS=true

# External Service URLs (for future integrations)
NOTIFICATION_SERVICE_URL=http://localhost:5004
EMAIL_SERVICE_URL=http://localhost:5005

# Monitoring and Analytics
ENABLE_METRICS=true
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_REQUEST_LOGGING=true

# Development Configuration
ENABLE_SWAGGER_DOCS=true
ENABLE_DEBUG_LOGS=true
