const mongoose = require('mongoose');

/**
 * Brand Schema for Catalog Management System
 * Implements comprehensive brand data structure
 */
const brandSchema = new mongoose.Schema({
  _id: {
    type: String,
    default: null
  },
  name: {
    type: String,
    unique: true,
    required: true,
    uniqueCaseInsensitive: true,
    trim: true,
    index: true
  },
  brandType: {
    type: String
  },
  image: [{
    type: String
  }],
  mainMobileImage: [String],
  mainDesktopImage: [String],
  description: {
    type: String
  },
  category: [{
    type: String
  }],
  status: {
    type: String,
    enum: ["Active", "Inactive"],
    required: true,
    index: true
  },
  audit: [{
    oldStatus: { type: String },
    newStatus: { type: String },
    remarks: { type: String },
    loggedAt: { type: Date },
    loggedBy: { type: String }
  }],
  deleted: {
    type: Boolean,
    default: false
  },
  createdBy: {
    type: String
  },
  modifiedBy: {
    type: String
  },
  createdAt: {
    type: Date
  },
  lastUpdated: {
    type: Date
  },
  remarks: {
    type: String
  },
  whIds: [
    { type: String }
  ],
  isExclusiveType: {
    type: Boolean
  },
  masterData: {
    isMaster: { type: Boolean, default: false },
    masterId: { type: String },
    vendorId: [{ type: String }]
  },
  url: {
    type: String
  },
  isValueBrand: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: false, // Using custom timestamp fields
  collection: 'brands'
});

// Indexes for better query performance
brandSchema.index({ name: 'text', description: 'text' });
brandSchema.index({ status: 1 });
brandSchema.index({ category: 1 });
brandSchema.index({ deleted: 1 });
brandSchema.index({ 'masterData.isMaster': 1 });
brandSchema.index({ 'masterData.masterId': 1 });
brandSchema.index({ whIds: 1 });
brandSchema.index({ createdAt: -1 });

// Pre-save middleware to handle timestamps and audit trail
brandSchema.pre('save', function(next) {
  const now = new Date();

  if (this.isNew) {
    this.createdAt = now;
  }
  this.lastUpdated = now;

  // Add audit entry if status is being modified
  if (this.isModified('status') && !this.isNew) {
    this.audit.push({
      oldStatus: this.constructor.findOne({ _id: this._id }).status,
      newStatus: this.status,
      remarks: this.remarks || 'Status updated',
      loggedAt: now,
      loggedBy: this.modifiedBy || 'system'
    });
  }

  next();
});

// Static method to find active brands
brandSchema.statics.findActive = function(options = {}) {
  return this.find({
    status: 'Active',
    deleted: false,
    ...options
  });
};

// Static method to find by category
brandSchema.statics.findByCategory = function(categoryId, options = {}) {
  return this.find({
    category: categoryId,
    status: 'Active',
    deleted: false,
    ...options
  });
};

// Static method to find master brands
brandSchema.statics.findMasterBrands = function(options = {}) {
  return this.find({
    'masterData.isMaster': true,
    status: 'Active',
    deleted: false,
    ...options
  });
};

// Method to soft delete
brandSchema.methods.softDelete = function(deletedBy) {
  this.deleted = true;
  this.modifiedBy = deletedBy;
  this.lastUpdated = new Date();
  return this.save();
};

// Method to add audit entry
brandSchema.methods.addAuditEntry = function(oldStatus, newStatus, remarks, loggedBy) {
  this.audit.push({
    oldStatus: oldStatus,
    newStatus: newStatus,
    remarks: remarks,
    loggedAt: new Date(),
    loggedBy: loggedBy
  });
  return this.save();
};

const Brand = mongoose.model('Brand', brandSchema);

module.exports = Brand;
