const mongoose = require('mongoose');

/**
 * <PERSON><PERSON> Price Configuration Schema for Catalog Management System
 * Manages pricing configurations for different entities (Deal, Category, Brand)
 */
const sellerPriceConfigSchema = new mongoose.Schema({
    id: {
        type: String
    },
    configOnType: {
        type: String,
        enum: ["Deal", "Category", "Brand"]
    },
    groupType: {
        type: String,
        enum: ["COCO", "FOFO", "DARKSTORE", "ALL"]
    },
    menu: {
        id: { type: String },
        name: { type: String }
    },
    brand: {
        id: { type: String },
        name: { type: String }
    },
    category: {
        id: { type: String },
        name: { type: String }
    },
    deal: {
        id: { type: String },
        name: { type: String },
        menu: {
            id: { type: String },
            name: { type: String }
        },
        brand: {
            id: { type: String },
            name: { type: String }
        },
        category: {
            id: { type: String },
            name: { type: String }
        }
    },
    franchiseInfo: {
        id: { type: String },
        name: { type: String },
        type: { type: String },
        subType: { type: String },
        sellerId: { type: String }
    },
    validityPeriod: {
        startDate: { type: Date },
        endDate: { type: Date }
    },
    isFixedPrice: {
        type: Boolean,
        default: false
    },
    fixedMrp: {
        type: Number
    },
    fixedPrice: {
        type: Number
    },
    hasOffer: {
        type: Boolean,
        default: false
    },
    isValidHasOffer: {
        type: Boolean,
        default: false
    },
    isValidOfferDay: {
        type: Boolean,
        default: false
    },
    offerOfTheDay: {
        isOfferOfTheDay: {
            type: Boolean,
            default: false
        },
        offerStartDate: { type: Date },
        offerEndDate: { type: Date },
        offerDiscount: { type: Number, default: 0 }
    },
    updateData: {
        type: Boolean,
        default: true
    },
    updateDataRefCode: {
        type: String
    },
    updateDataLog: [{
        refCode: { type: String },
        discountType: {
            type: String,
            enum: ["Normal", "Fixed", "SpecialDiscount", "OfferOfTheDay"]
        },
        discountValue: { type: Number },
        updatedBy: { type: String },
        updatedOn: { type: String },
        orderCount: { type: Number },
        orderAmount: { type: Number }
    }],
    auditLog: [{
        oldData: { type: String },
        newData: { type: String },
        paramName: { type: String },
        loggedAt: { type: Date },
        loggedBy: { type: String },
        message: { type: String }
    }],
    discount: {
        type: Number,
        required: true
    },
    specialDiscount: {
        type: Number,
        default: 0
    },
    status: {
        type: String,
        default: "Active",
        enum: ["Active", "Inactive"]
    },
    createdBy: { type: String },
    createdAt: { type: Date },
    modifiedBy: { type: String },
    modifiedAt: { type: Date },
    createdByType: { type: String },
    modifiedByType: { type: String },
    lastUpdated: { type: Date },
    applicableFor: {
        type: String,
        enum: ["Customer", "Network"],
        default: "Customer"
    },
    networkConfig: {
        by: {
            type: String,
            enum: ["Overall", "Franchise"]
        },
        fid: { type: String },
        name: { type: String },
        type: { type: String },
        subType: { type: String },
        sellerId: { type: String }
    }
}, {
    collection: 'sellerpriceconfigs'
});

// Indexes for better query performance
sellerPriceConfigSchema.index({ configOnType: 1, status: 1 });
sellerPriceConfigSchema.index({ groupType: 1, status: 1 });
sellerPriceConfigSchema.index({ 'menu.id': 1 });
sellerPriceConfigSchema.index({ 'brand.id': 1 });
sellerPriceConfigSchema.index({ 'category.id': 1 });
sellerPriceConfigSchema.index({ 'deal.id': 1 });
sellerPriceConfigSchema.index({ 'franchiseInfo.id': 1 });
sellerPriceConfigSchema.index({ 'franchiseInfo.sellerId': 1 });
sellerPriceConfigSchema.index({ applicableFor: 1, status: 1 });
sellerPriceConfigSchema.index({ 'validityPeriod.startDate': 1, 'validityPeriod.endDate': 1 });
sellerPriceConfigSchema.index({ createdAt: -1 });

// Pre-save middleware for timestamps
sellerPriceConfigSchema.pre('save', function(next) {
    const now = new Date();

    if (this.isNew) {
        this.createdAt = now;
    }
    this.modifiedAt = now;
    this.lastUpdated = now;

    next();
});

// Method to add audit log entry
sellerPriceConfigSchema.methods.addAuditEntry = function(oldData, newData, paramName, loggedBy, message) {
    this.auditLog.push({
        oldData: JSON.stringify(oldData),
        newData: JSON.stringify(newData),
        paramName,
        loggedAt: new Date(),
        loggedBy,
        message
    });
};

// Method to add update data log entry
sellerPriceConfigSchema.methods.addUpdateDataEntry = function(refCode, discountType, discountValue, updatedBy, orderCount = 0, orderAmount = 0) {
    this.updateDataLog.push({
        refCode,
        discountType,
        discountValue,
        updatedBy,
        updatedOn: new Date().toISOString(),
        orderCount,
        orderAmount
    });
};

// Static method to find active configurations
sellerPriceConfigSchema.statics.findActiveConfigs = function(filters = {}) {
    return this.find({
        ...filters,
        status: 'Active'
    });
};

// Static method to find configurations by type
sellerPriceConfigSchema.statics.findByConfigType = function(configOnType, groupType = null) {
    const query = { configOnType, status: 'Active' };
    if (groupType) {
        query.groupType = groupType;
    }
    return this.find(query);
};

// Static method to find configurations by validity period
sellerPriceConfigSchema.statics.findValidConfigs = function(date = new Date()) {
    return this.find({
        status: 'Active',
        $or: [
            { 'validityPeriod.startDate': { $exists: false } },
            {
                'validityPeriod.startDate': { $lte: date },
                'validityPeriod.endDate': { $gte: date }
            }
        ]
    });
};

// Static method to find configurations by franchise
sellerPriceConfigSchema.statics.findByFranchise = function(franchiseId, sellerId = null) {
    const query = {
        'franchiseInfo.id': franchiseId,
        status: 'Active'
    };
    if (sellerId) {
        query['franchiseInfo.sellerId'] = sellerId;
    }
    return this.find(query);
};

// Static method to find offer of the day configurations
sellerPriceConfigSchema.statics.findOfferOfTheDay = function(date = new Date()) {
    return this.find({
        status: 'Active',
        'offerOfTheDay.isOfferOfTheDay': true,
        'offerOfTheDay.offerStartDate': { $lte: date },
        'offerOfTheDay.offerEndDate': { $gte: date }
    });
};

const SellerPriceConfig = mongoose.model('SellerPriceConfig', sellerPriceConfigSchema);

module.exports = SellerPriceConfig;
