const mongoose = require('mongoose');

/**
 * Category Schema for Catalog Management System
 * Implements hierarchical category structure with comprehensive business logic
 */
const categorySchema = new mongoose.Schema({
    _id: {
        type: String,
        default: null
    },
    name: {
        type: String,
        required: true,
        minlength: 1,
        maxlength: 1024,
        unique: true,
        uniqueCaseInsensitive: true
    },
    parent: {
        type: String
    },
    fulfillmentPolicys: [{
        type: String,
        enum: ["FIFO", "Expiry date", "Highest MRP", "Lowest MRP",
            "Offer First", "Offer Last", "Highest Cost", "Lowest Cost", "Expiry First", "Expiry Last", "LILO","Latest MRP"],
        default: "Latest MRP"
    }],
    maxB2BQuantity: {
        type: Number,
        min: 0,
        max: 100000
    },
    maxB2CQuantity: {
        type: Number,
        min: 0,
        max: 100000
    },
    images: [{
        image: {
            type: String,
            minlength: 1,
            maxlength: 4096
        }
    }],
    collectSerialNumber: {
        type: Boolean,
        default: false
    },
    maxB2CPeriod: {
        type: Number
    },
    maxB2BPeriod: {
        type: Number
    },
    maxB2BPeriodType: {
        type: String,
        enum: ["Days", "Month"],
        default: "Days"
    },
    maxB2CPeriodType: {
        type: String,
        enum: ["Days", "Month"],
        default: "Days"
    },
    status: {
        type: String,
        enum: ["Active", "Inactive"]
    },
    stickyOrderPrice: {
        type: Boolean,
        default: false
    },
    marginDealerPrice: {
        type: Boolean,
        default: false
    },
    hasParentCategory: {
        type: Boolean,
        default: false
    },
    categoryType:{
        type: String
    },
    categorySubtype:{
        type: String
    },
    noReturnsAllowed: {
        type: Boolean,
        default: true
    },
    displayDealMrp: {
        type: Boolean,
        default: true
    },
    returnDays: {
        type: Number
    },
    //this field we will consider when we are doing invoice if this field is true then we will do
    //invoice on order amount if this is false then refundOnMrpChange,debitOnMrpChange this will be considered.
    invoiceOnOrderAmount: {
        type: Boolean,
        default: false
    },
    //this field we will consider while invoicing if this field is true means then we will need to refund the amount when mrp changes
    refundOnMrpChange: {
        type: Boolean,
        default: false
    },
    //this field we will consider while invoicing if this field is true means then we can debit the amount if the mrp more
    debitOnMrpChange: {
        type: Boolean,
        default: false
    },
    attributes: [{
        name: {
            type: String,
            required: true,
            minlength: 1,
            maxlength: 1024
        },
        type: {
            type: String,
            enum: ["Text", "Number", "TextArea", "Select", "Radio"],
            required: true
        },
        min: {
            type: Number
        },
        max: {
            type: Number
        },
        pattern: {
            type: String
        },
        csd: {
            type: String
        },
        mandatory: {
            type: Boolean
        },
        predefined: {
            type: Boolean,
            default: false
        }
    }],
    remarks: {
        type: String
    },
    audit: [{
        oldStatus: { type: String },
        newStatus: { type: String },
        remarks: { type: String },
        loggedAt: { type: Date },
        loggedBy: { type: String }
    }],
    updateChild: {
        type: Boolean,
        default: false
    },
    createdBy: {
        type: String
    },
    modifiedBy: {
        type: String
    },
    createdAt: {
        type: Date
    },
    lastUpdated: {
        type: Date
    },
    deleted: {
        type: Boolean,
        default: false
    },
    whIds: [
        { type: String }
    ],
    isExclusiveType: {
        type: Boolean
    },
    focusedCategoryInfo: {
        isFocusedCategory: {
            type: Boolean,
            default: false
            },
        tagLine: { type: String }
    },
    dailyNeedsInfo: {
        isDailyNeeds: {
            type: Boolean,
            default: false
            },
        tagLine: { type: String }
    },
    isRestrictedForSeller: {
        type: Boolean,
        default: false
    },
    description: {
        type: String
    },
    classGroup: {
        _id: {
            type: String//This Can be ANything , and this will be intitailly Configured from the Sourcing Team
        },
        name: {
            type: String
        }
    },
    classifications: [//This can King,Queen,Ace ... and this will also configured from the User
        {
            _id: {
                type: String
            },
            name: {
                type: String
            }
        }
    ],
    pyramidClassification: {
        _id: {
            type: String//This Can be ANything , and this will be intitailly Configured from the Sourcing Team
        },
        name: {
            type: String
        }
    },
    masterMenu: {
        type: String//this will be one more master menu like FMCG,Electronics Like that
    },
    masterMenuId: {
        type: String
    },
    url: {
        type: String
    },
    tierData: [{
        name: {
            type: String,
            enum: ["Tier 1", "Tier 2", "Tier 3", "Tier 4", "Tier 5", "Tier 6", "Tier 7", "Metro"]
        },
        value: { type: Number }, // 25000
        valueType: { type: String, default: "value", enum: ["percentage", "value"] }
    }],
    mainImages: [String],
    assignedGroups: [{
        type: String
    }],
    isFmcgClassGroup: {
        type: Boolean,
        default: false
    },
    department: {
        _id: {type: String},
        name: {type: String}
    }
}, {
    collection: 'categories'
});

// Indexes for better query performance
categorySchema.index({ name: 'text', description: 'text' });
categorySchema.index({ parent: 1, status: 1 });
categorySchema.index({ status: 1 });
categorySchema.index({ createdAt: -1 });
categorySchema.index({ deleted: 1 });
categorySchema.index({ whIds: 1 });
categorySchema.index({ masterMenuId: 1 });

// Pre-save middleware for audit trail and timestamps
categorySchema.pre('save', function(next) {
  const now = new Date();

  if (this.isNew) {
    this.createdAt = now;
  }
  this.lastUpdated = now;

  next();
});

// Static method to get category tree
categorySchema.statics.getCategoryTree = async function(parentId = null) {
  const categories = await this.find({
    parent: parentId,
    status: 'Active',
    deleted: false
  }).sort({ name: 1 });

  const tree = [];

  for (const category of categories) {
    const categoryObj = category.toObject();
    categoryObj.children = await this.getCategoryTree(category._id);
    tree.push(categoryObj);
  }

  return tree;
};

// Static method to find all descendants
categorySchema.statics.findDescendants = async function(categoryId) {
  const descendants = [];
  const queue = [categoryId];

  while (queue.length > 0) {
    const currentId = queue.shift();
    const children = await this.find({
      parent: currentId,
      deleted: false
    });

    for (const child of children) {
      descendants.push(child);
      queue.push(child._id);
    }
  }

  return descendants;
};

// Method to add audit entry
categorySchema.methods.addAuditEntry = function(oldStatus, newStatus, remarks, loggedBy) {
  this.audit.push({
    oldStatus,
    newStatus,
    remarks,
    loggedAt: new Date(),
    loggedBy
  });
};

const Category = mongoose.model('Category', categorySchema);

module.exports = Category;
