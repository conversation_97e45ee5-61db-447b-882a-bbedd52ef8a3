const brandService = require('../services/brandService');

/**
 * Brand Controller
 * Handles HTTP requests for brand management operations
 */

/**
 * Create a new brand
 * POST /brands
 */
const createBrand = async (req, res, next) => {
  try {
    const brandData = req.body;
    const result = await brandService.createBrand(brandData);

    res.status(201).json({
      success: true,
      message: result.message,
      data: result.brand,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get all brands with pagination and filtering
 * GET /brands
 */
const getBrands = async (req, res, next) => {
  try {
    const { page = 1, limit = 10, status, search } = req.query;
    const filters = { status, search };
    
    const result = await brandService.getBrands(page, limit, filters);

    res.status(200).json({
      success: true,
      message: 'Brands retrieved successfully',
      data: result.brands,
      pagination: result.pagination,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get brand by ID
 * GET /brands/:id
 */
const getBrandById = async (req, res, next) => {
  try {
    const { id } = req.params;
    const result = await brandService.getBrandById(id);

    res.status(200).json({
      success: true,
      message: 'Brand retrieved successfully',
      data: result.brand,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update brand by ID
 * PUT /brands/:id
 */
const updateBrand = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    const result = await brandService.updateBrand(id, updateData);

    res.status(200).json({
      success: true,
      message: result.message,
      data: result.brand,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete brand by ID (soft delete)
 * DELETE /brands/:id
 */
const deleteBrand = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { deletedBy } = req.body;
    const result = await brandService.deleteBrand(id, deletedBy);

    res.status(200).json({
      success: true,
      message: result.message,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get active brands
 * GET /brands/active
 */
const getActiveBrands = async (req, res, next) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const result = await brandService.getActiveBrands(page, limit);

    res.status(200).json({
      success: true,
      message: 'Active brands retrieved successfully',
      data: result.brands,
      pagination: result.pagination,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get brands by category
 * GET /brands/category/:categoryId
 */
const getBrandsByCategory = async (req, res, next) => {
  try {
    const { categoryId } = req.params;
    const { limit = 10 } = req.query;
    const result = await brandService.getBrandsByCategory(categoryId, limit);

    res.status(200).json({
      success: true,
      message: 'Brands retrieved successfully',
      data: result.brands,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get master brands
 * GET /brands/master
 */
const getMasterBrands = async (req, res, next) => {
  try {
    const { limit = 10 } = req.query;
    const result = await brandService.getMasterBrands(limit);

    res.status(200).json({
      success: true,
      message: 'Master brands retrieved successfully',
      data: result.brands,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update brand status
 * PATCH /brands/:id/status
 */
const updateBrandStatus = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { status, remarks, updatedBy } = req.body;
    const result = await brandService.updateBrandStatus(id, status, remarks, updatedBy);

    res.status(200).json({
      success: true,
      message: result.message,
      data: result.brand,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  createBrand,
  getBrands,
  getBrandById,
  updateBrand,
  deleteBrand,
  getActiveBrands,
  getBrandsByCategory,
  getMasterBrands,
  updateBrandStatus
};
