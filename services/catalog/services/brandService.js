const Brand = require('../models/Brand');

/**
 * Brand Service
 * Business logic for brand management operations
 */

/**
 * Create a new brand
 */
const createBrand = async (brandData) => {
  try {
    const brand = new Brand(brandData);
    await brand.save();

    return {
      success: true,
      message: 'Brand created successfully',
      brand: brand
    };
  } catch (error) {
    if (error.code === 11000) {
      if (error.keyPattern.name) {
        throw new Error('Brand with this name already exists');
      }
    }
    throw error;
  }
};

/**
 * Get brands with pagination and filtering
 */
const getBrands = async (page = 1, limit = 10, filters = {}) => {
  try {
    const skip = (page - 1) * limit;
    const query = { deleted: false };

    // Apply filters
    if (filters.status) {
      query.status = filters.status;
    }
    if (filters.category) {
      query.category = filters.category;
    }
    if (filters.isValueBrand !== undefined) {
      query.isValueBrand = filters.isValueBrand;
    }
    if (filters.isExclusiveType !== undefined) {
      query.isExclusiveType = filters.isExclusiveType;
    }
    if (filters.search) {
      query.$text = { $search: filters.search };
    }

    const brands = await Brand.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Brand.countDocuments(query);

    return {
      success: true,
      brands: brands,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: parseInt(limit)
      }
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get brand by ID
 */
const getBrandById = async (brandId) => {
  try {
    const brand = await Brand.findById(brandId);

    if (!brand) {
      throw new Error('Brand not found');
    }

    return {
      success: true,
      brand: brand
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Update brand by ID
 */
const updateBrand = async (brandId, updateData) => {
  try {
    const brand = await Brand.findByIdAndUpdate(
      brandId,
      { ...updateData, lastUpdated: new Date() },
      { new: true, runValidators: true }
    );

    if (!brand) {
      throw new Error('Brand not found');
    }

    return {
      success: true,
      message: 'Brand updated successfully',
      brand: brand
    };
  } catch (error) {
    if (error.code === 11000) {
      if (error.keyPattern.name) {
        throw new Error('Brand with this name already exists');
      }
    }
    throw error;
  }
};

/**
 * Delete brand by ID (soft delete)
 */
const deleteBrand = async (brandId, deletedBy) => {
  try {
    const brand = await Brand.findById(brandId);

    if (!brand) {
      throw new Error('Brand not found');
    }

    await brand.softDelete(deletedBy);

    return {
      success: true,
      message: 'Brand deleted successfully'
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get active brands
 */
const getActiveBrands = async (page = 1, limit = 10) => {
  try {
    const skip = (page - 1) * limit;

    const brands = await Brand.findActive()
      .sort({ name: 1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Brand.countDocuments({ status: 'Active', deleted: false });

    return {
      success: true,
      brands: brands,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: parseInt(limit)
      }
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get brands by category
 */
const getBrandsByCategory = async (categoryId, limit = 10) => {
  try {
    const brands = await Brand.findByCategory(categoryId)
      .sort({ name: 1 })
      .limit(parseInt(limit));

    return {
      success: true,
      brands: brands
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get master brands
 */
const getMasterBrands = async (limit = 10) => {
  try {
    const brands = await Brand.findMasterBrands()
      .sort({ name: 1 })
      .limit(parseInt(limit));

    return {
      success: true,
      brands: brands
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Update brand status with audit trail
 */
const updateBrandStatus = async (brandId, newStatus, remarks, updatedBy) => {
  try {
    const brand = await Brand.findById(brandId);

    if (!brand) {
      throw new Error('Brand not found');
    }

    const oldStatus = brand.status;
    brand.status = newStatus;
    brand.modifiedBy = updatedBy;
    brand.remarks = remarks;

    await brand.addAuditEntry(oldStatus, newStatus, remarks, updatedBy);

    return {
      success: true,
      message: 'Brand status updated successfully',
      brand: brand
    };
  } catch (error) {
    throw error;
  }
};

module.exports = {
  createBrand,
  getBrands,
  getBrandById,
  updateBrand,
  deleteBrand,
  getActiveBrands,
  getBrandsByCategory,
  getMasterBrands,
  updateBrandStatus
};
