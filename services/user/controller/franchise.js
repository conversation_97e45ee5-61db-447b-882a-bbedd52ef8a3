const franchiseService = require('../services/franchise');




const getFranchiseById = async (req, res, next) => {
  try {
     const franchiseId=req.user.franchise
    await franchiseService.getFranchiseById(franchiseId).then(response => {
    if (Object.keys(response).length > 0) {
        res.status(200).send(response)
    }else{
         res.status(400).send({"message":"franchise not found"})
    }
    }).catch(error => {
      res.status(500).send({ "message": error })
    })
  } catch (err) {
    res.status(500).send({ "message": err })
  }
}



module.exports={
   getFranchiseById 
}
