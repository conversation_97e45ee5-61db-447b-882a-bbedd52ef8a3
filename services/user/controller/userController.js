const userService = require('../services/userService');
const bcrypt = require('bcryptjs');

/**
 * User Controller
 * Handles HTTP requests for user management operations
 */

/**
 * Create a new user
 * POST /users
 */
const createUser = async (req, res, next) => {
  try {
    let userData = req.body;
    // Hash password with bcrypt
    if (userData.password) {
      const saltRounds = 10;
      userData.password = await bcrypt.hash(userData.password, saltRounds);
    }

    const result = await userService.createUser(userData);

    res.status(201).json({
      success: true,
      message: result.message,
      data: result.user,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error(`[${req.requestId}] Error creating user:`, error);
    next(error);
  }
};

/**
 * Get user by ID
 * GET /users/:id
 */
const getUserById = async (req, res, next) => {
  try {
    const userId = req.params.id;
    console.log(`[${req.requestId}] Getting user: ${userId}`);

    const result = await userService.getUserById(userId);

    res.status(200).json({
      success: true,
      message: result.message,
      data: result.user,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error(`[${req.requestId}] Error getting user:`, error);
    next(error);
  }
};

/**
 * Update an existing user
 * PUT /users/:id
 */
const updateUser = async (req, res, next) => {
  try {
    const userId = req.params.id;
    const updateData = req.body;
    const modifiedBy = req.user ? req.user.id : 'system';
    const requestingUser = req.user;

    console.log(`[${req.requestId}] Updating user: ${userId}`);

    // Exclude password field from updates - password updates should be handled separately
    const { password, ...dataToUpdate } = updateData;

    const result = await userService.updateUser(userId, dataToUpdate, modifiedBy, requestingUser);

    res.status(200).json({
      success: true,
      message: result.message,
      data: result.user,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error(`[${req.requestId}] Error updating user:`, error);
    next(error);
  }
};

/**
 * User login
 * POST /auth/login
 */
const login = async (req, res, next) => {
  try {
    const loginData = req.body;
    await userService.loginUser(loginData).then(response => {
      res.status(200).send(response)
    }).catch(error => {
      res.status(400).send({ "message": error.message?error.message:"invalid mobile number or password"})
    })
  } catch (err) {
    res.status(500).send({ "message": "something went wrong-login" })
  }
}





module.exports = {
  createUser,
  getUserById,
  updateUser,
  login,
}
