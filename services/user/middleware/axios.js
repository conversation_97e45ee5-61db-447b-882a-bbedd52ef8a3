const axios = require('axios');

// You can customize the axios instance here (e.g., set baseURL, interceptors, timeouts, etc.)
const instance = axios.create({
  timeout: 30000, // Default timeout
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
  // Add more global config if needed
});

// Example: Add a request interceptor
// instance.interceptors.request.use(config => {
//   // Modify config if needed
//   return config;
// }, error => Promise.reject(error));

// Example: Add a response interceptor
// instance.interceptors.response.use(response => response, error => Promise.reject(error));

module.exports = instance; 