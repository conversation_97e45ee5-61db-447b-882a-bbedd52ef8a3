const User = require('../models/User');
const UserAuth = require('../models/UserAuth');
const axiosInstance = require('../middleware/axios');
const franchise = require('../models/franchise');

function getAxios() {
  return axiosInstance;
}

const UAT_BASE_URL = 'https://uat.storeking.in/apigateway/api';
const FRANCHISE_BASE_URL = 'http://localhost:5003/franchise';
const ALLOWED_FRANCHISE_TYPE = 'SF';
const ALLOWED_FRANCHISE_SUBTYPES = ['SFSELLER', 'SFBUYER', 'SMARTSFCOCO'];




// Helper to map documents
function mapDocuments(response) {
  const documents = [];
  // Address (Aadhar)
  response.sk_franchise_details?.mandatory_documents?.address?.forEach(doc => {
    documents.push({
      type: 'Aadhar',
      refNumber: doc.addressProofNo,
      fileUrl: doc.addressProofFile,
      issuedBy: doc.approvedBy,
      issuedDate: doc.approvedOn,
      verified: doc.addressProofIdStatus === 'Approved',
      uploadedOn: doc.uploadedOn
    });
  });
  // PAN
  response.sk_franchise_details?.mandatory_documents?.photo?.forEach(doc => {
    documents.push({
      type: 'PAN',
      refNumber: doc.photoIDNo,
      fileUrl: doc.photoIDFile,
      issuedBy: doc.approvedBy,
      issuedDate: doc.approvedOn,
      verified: doc.photoIdStatus === 'Approved',
      uploadedOn: doc.uploadedOn
    });
  });
  // GST
  response.sk_franchise_details?.mandatory_documents?.business?.forEach(doc => {
    documents.push({
      type: 'GST',
      refNumber: doc.businessIDNo,
      fileUrl: doc.businessIDFile,
      issuedBy: doc.approvedBy,
      issuedDate: doc.approvedOn,
      verified: doc.businessIDStatus === 'Approved',
      uploadedOn: doc.uploadedOn
    });
  });
  // Shop Photos
  response.shop_photos_details?.forEach(doc => {
    documents.push({
      type: 'ShopPhoto',
      fileUrl: doc.name,
      issuedBy: doc.approvedBy,
      issuedDate: doc.approvedOn,
      verified: doc.status === 'Approved',
      uploadedOn: doc.uploadedOn
    });
  });
  return documents;
}

// Helper to map status logs
function mapStatusLogs(response) {
  return (response.manualStatusUpdateLog || []).map(log => ({
    status: log.status,
    updatedBy: {
      id: log.updatedBy,
      name: '',
      role: ''
    },
    remarks: log.remarks,
    updatedOn: log.updatedAt
  }));
}

// Helper to map kycStatus
function mapKycStatus(documents, response) {
  return {
    businessProofVerified: !!documents.find(d => d.type === 'BusinessProof' && d.verified),
    gstVerified: !!documents.find(d => d.type === 'GST' && d.verified),
    addressVerified: !!documents.find(d => d.type === 'Aadhar' && d.verified),
    panVerified: !!documents.find(d => d.type === 'PAN' && d.verified),
    aadharVerified: !!documents.find(d => d.type === 'Aadhar' && d.verified),
    overallStatus: response.status || 'Pending'
  };
}

async function saveFranchises(response) {
  // const result = await franchise.find({ franchiseId: response._id });
  // if (result.length > 0) return result;

  const owner = (response.contact_details && response.contact_details[0]) || {};
  const finance = response.finance_details || {};
  const address = response.address || {};
  const geo = (response.shop_details && response.shop_details.geolocation && response.shop_details.geolocation.coordinates) || [];
  const latitude = geo[1] || null;
  const longitude = geo[0] || null;

  const documents = mapDocuments(response);
  const statusLogs = mapStatusLogs(response);
  const kycStatus = mapKycStatus(documents, response);

  const franchiseData = {
    franchiseId: response._id,
    type: response.sk_franchise_details?.franchise_type || 'SF',
    subType: response.sk_franchise_details?.franchise_sub_type || 'SFSELLER',
    groupType: response.groupType || 'COCO',
    name: response.name,
    email: owner.email || '',
    mobile: String(owner.mobile || response.whatsAppNumber || response.username || ''),
    altMobile: '',
    gstNumber: finance.gstNo || '',
    sizeSqFt: undefined,
    ownershipType: undefined,
    rentAgreementUrl: undefined,
    addressLine1: address.door_no || '',
    addressLine2: address.street || '',
    city: response.town || '',
    district: response.district || '',
    state: response.state || '',
    pincode: String(response.pincode || ''),
    latitude,
    longitude,
    ownerDetails: {
      name: owner.name || '',
      aadharNumber: '',
      panNumber: finance.pan_no || '',
      idProofUrl: '',
      photoUrl: '',
    },
    documents,
    kycStatus,
    statusLogs,
    createdAt: response.createdAt,
    updatedAt: response.lastUpdated || response.updatedAt
  };

  try {
    // TODO: Replace with actual service URL if needed
    const res = await getAxios().post(FRANCHISE_BASE_URL, franchiseData, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      timeout: 30000
    });
    return res.data.data; // Assuming the franchise service returns { success, message, data }
  } catch (err) {
    const statusCode = err.response?.status || 500;
    const message = err.response?.data?.message || 'Failed to create franchise via franchise service.';
    throw { statusCode, message, error: err };
  }
}

async function saveOrUpdateUserToken(userId, token) {
  // Find if a token already exists for this user
  const existing = await UserAuth.findOne({ userId });
  if (existing) {
    existing.token = token;
    await existing.save();
    return existing;
  } else {
    const newToken = new UserAuth({ userId, token });
    await newToken.save();
    return newToken;
  }
}

const loginUser = async (loginData) => {
  try {
    const externalLoginPayload = {
      username: loginData.mobileNo,
      password: loginData.password,
      appType: "Retailer",
      userType: "Franchise",
      imeiNumber: "",
      appVersion: "v3"
    };
    let response;
    try {
      response = await getAxios().post(`${UAT_BASE_URL}/user/v1/login`, externalLoginPayload, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        timeout: 30000
      });
    } catch (err) {
      const statusCode = err.response?.status || 500;
      const message = err.response?.data?.message || 'Failed to login. Please check your credentials or try again later.';
      throw { statusCode, message };
    }
    if (response) {
      let franchiseRes;
      try {
        franchiseRes = await getAxios().get(`${UAT_BASE_URL}/franchise/v1/${response.data.franchise}`, {
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `JWT ${response.data.token}`
          },
          timeout: 30000
        });
      } catch (err) {
        const statusCode = err.response?.status || 500;
        const message = err.response?.data?.message || 'Failed to fetch franchise details.';
        throw { statusCode, message, error: err };
      }
      if (franchiseRes) {
        // Check franchise type and subtype
        const franchiseType = franchiseRes.data?.sk_franchise_details?.franchise_type;
        const franchiseSubType = franchiseRes.data?.sk_franchise_details?.franchise_sub_type;
        if (franchiseType !== ALLOWED_FRANCHISE_TYPE || !ALLOWED_FRANCHISE_SUBTYPES.includes(franchiseSubType)) {
          throw {
            statusCode: 403,
            message: 'Only SF franchise with allowed subtypes can login.'
          };
        }
        try {
          await saveFranchises(franchiseRes.data);
        } catch (err) {
          console.log("error in saveFranchises", err);
          throw { statusCode: 500, message: 'Failed to save franchise details.', error: err };
        }
      }
      try {
        await saveOrUpdateUserToken(response.data._id, response.data.token);
      } catch (err) {
        throw { statusCode: 500, message: 'Failed to save user token.', error: err };
      }
    }
    return response.data;
  } catch (error) {
    const statusCode = error.statusCode || 500;
    const message = error.message || 'An unexpected error occurred during login.';
    throw { statusCode, message, error };
  }
};

module.exports = {
  loginUser,
  saveFranchises,
  saveOrUpdateUserToken
};
